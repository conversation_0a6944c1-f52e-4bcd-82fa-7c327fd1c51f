#!/usr/bin/env node

/**
 * BladeRF IQ Recording Test
 * 
 * This script tests the IQ recording functionality of the BladeRF addon.
 * It demonstrates recording IQ data to WAV files with different configurations.
 */

const BladeRF = require('./index.js');
const fs = require('fs');
const path = require('path');

console.log('🎙️  BladeRF IQ Recording Test\n');

// Create BladeRF instance
const bladerf = new BladeRF();

async function testIQRecording() {
    try {
        // Test 1: Basic functionality
        console.log('1. Testing basic IQ recording functionality:');
        console.log('   Hello World:', bladerf.helloWorld());
        console.log('   Library version:', bladerf.getLibraryVersion());
        console.log('   ✅ Basic functionality working\n');

        // Test 2: Check for devices
        console.log('2. Checking for BladeRF devices:');
        const devices = bladerf.getDeviceList();
        console.log(`   Found ${devices.length} device(s)`);
        
        if (devices.length === 0) {
            console.log('   ⚠️  No BladeRF devices found');
            console.log('   📝 Testing IQ recording API without hardware...\n');
            
            // Test API without hardware
            console.log('3. Testing IQ recording API (no hardware):');
            
            try {
                bladerf.startIQRecording('test.wav', 5);
                console.log('   ❌ Should have failed (no device open)');
            } catch (error) {
                console.log('   ✅ Correctly failed when device not open:', error.message);
            }
            
            console.log('   ✅ API validation working correctly\n');
            
            console.log('🎉 IQ Recording API test completed!');
            console.log('📝 To test with real hardware:');
            console.log('   1. Connect a BladeRF device');
            console.log('   2. Run this test again');
            return;
        }

        // Test 3: Open device
        console.log('3. Opening BladeRF device:');
        if (!bladerf.openDevice()) {
            throw new Error('Failed to open BladeRF device');
        }
        console.log('   ✅ Device opened successfully\n');

        // Test 4: Configure device for recording
        console.log('4. Configuring device for IQ recording:');
        const config = {
            frequency: 915000000,    // 915 MHz
            sampleRate: 1000000,     // 1 MHz
            bandwidth: 1500000,      // 1.5 MHz
            gain: 30                 // 30 dB
        };
        
        if (bladerf.configure(config)) {
            console.log('   ✅ Device configured successfully');
            const currentConfig = bladerf.getCurrentConfig();
            console.log(`   📊 Sample Rate: ${currentConfig.sampleRate} Hz`);
            console.log(`   📡 Frequency: ${currentConfig.frequency} Hz`);
            console.log(`   📏 Bandwidth: ${currentConfig.bandwidth} Hz`);
            console.log(`   🔊 Gain: ${currentConfig.gain} dB`);
        } else {
            console.log('   ⚠️  Device configuration failed (may be normal without firmware)');
        }
        console.log();

        // Test 5: Short IQ recording test
        console.log('5. Testing short IQ recording (3 seconds):');
        const shortFilename = 'test_short_iq.wav';
        
        console.log(`   📁 Recording to: ${shortFilename}`);
        console.log('   ⏱️  Duration: 3 seconds');
        
        if (bladerf.startIQRecording(shortFilename, 3)) {
            console.log('   ✅ Recording started successfully');
            
            // Monitor recording progress
            let lastSamples = 0;
            const monitorInterval = setInterval(() => {
                if (bladerf.isRecording()) {
                    const duration = bladerf.getRecordingDuration();
                    const samples = bladerf.getRecordedSamples();
                    const sampleRate = samples - lastSamples;
                    lastSamples = samples;
                    
                    console.log(`   📊 Recording: ${duration.toFixed(2)}s, ${samples} samples (${sampleRate}/s)`);
                } else {
                    clearInterval(monitorInterval);
                    const finalDuration = bladerf.getRecordingDuration();
                    const finalSamples = bladerf.getRecordedSamples();
                    console.log(`   ✅ Recording completed: ${finalDuration.toFixed(2)}s, ${finalSamples} samples`);
                    
                    // Check file
                    if (fs.existsSync(shortFilename)) {
                        const stats = fs.statSync(shortFilename);
                        console.log(`   📁 File size: ${stats.size} bytes`);
                        console.log(`   📊 Expected samples: ${Math.floor(finalDuration * config.sampleRate)}`);
                    }
                }
            }, 500);
            
            // Wait for recording to complete
            await new Promise(resolve => {
                const checkComplete = () => {
                    if (!bladerf.isRecording()) {
                        resolve();
                    } else {
                        setTimeout(checkComplete, 100);
                    }
                };
                checkComplete();
            });
            
        } else {
            console.log('   ❌ Failed to start recording');
        }
        console.log();

        // Test 6: Test recording with different sample rates
        console.log('6. Testing recording with different sample rates:');
        
        const testConfigs = [
            { sampleRate: 500000, duration: 2, filename: 'test_500k.wav' },
            { sampleRate: 2000000, duration: 1, filename: 'test_2m.wav' }
        ];
        
        for (const testConfig of testConfigs) {
            console.log(`   📊 Testing ${testConfig.sampleRate} Hz for ${testConfig.duration}s`);
            
            // Configure sample rate
            if (bladerf.setSampleRate(testConfig.sampleRate)) {
                console.log(`   ✅ Sample rate set to ${testConfig.sampleRate} Hz`);
                
                // Record
                try {
                    const result = await bladerf.recordIQ(testConfig.filename, testConfig.duration);
                    console.log(`   ✅ Recording completed: ${result.duration.toFixed(2)}s, ${result.samples} samples`);
                    
                    if (fs.existsSync(testConfig.filename)) {
                        const stats = fs.statSync(testConfig.filename);
                        console.log(`   📁 File: ${testConfig.filename} (${stats.size} bytes)`);
                    }
                } catch (error) {
                    console.log(`   ❌ Recording failed: ${error.message}`);
                }
            } else {
                console.log(`   ⚠️  Failed to set sample rate to ${testConfig.sampleRate} Hz`);
            }
        }
        console.log();

        // Test 7: Test manual start/stop
        console.log('7. Testing manual start/stop recording:');
        const manualFilename = 'test_manual.wav';
        
        if (bladerf.startIQRecording(manualFilename, 10)) { // 10 second max
            console.log('   ✅ Manual recording started');
            
            // Record for 2 seconds then stop manually
            setTimeout(() => {
                if (bladerf.isRecording()) {
                    console.log('   ⏹️  Stopping recording manually...');
                    bladerf.stopIQRecording();
                    
                    const duration = bladerf.getRecordingDuration();
                    const samples = bladerf.getRecordedSamples();
                    console.log(`   ✅ Manual stop completed: ${duration.toFixed(2)}s, ${samples} samples`);
                }
            }, 2000);
            
            // Wait for stop
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
        console.log();

        // Test 8: Close device
        console.log('8. Cleaning up:');
        bladerf.closeDevice();
        console.log('   ✅ Device closed\n');

        // Test 9: Show recorded files
        console.log('9. Recorded files summary:');
        const wavFiles = [shortFilename, ...testConfigs.map(c => c.filename), manualFilename];
        
        for (const filename of wavFiles) {
            if (fs.existsSync(filename)) {
                const stats = fs.statSync(filename);
                console.log(`   📁 ${filename}: ${stats.size} bytes`);
            }
        }
        
        console.log('\n🎉 IQ Recording test completed successfully!');
        console.log('\n📝 WAV File Format:');
        console.log('   - 2 channels (I and Q)');
        console.log('   - 16-bit signed integers');
        console.log('   - Sample rate matches BladeRF configuration');
        console.log('   - Standard WAV format (playable in audio software)');
        
        console.log('\n💡 Usage examples:');
        console.log('   - Load in GNU Radio for signal analysis');
        console.log('   - Import into Audacity as raw audio');
        console.log('   - Process with custom DSP applications');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
        
        // Cleanup
        try {
            if (bladerf.isRecording()) {
                console.log('\n🧹 Stopping any active recording...');
                bladerf.stopIQRecording();
            }
            if (bladerf.isDeviceOpen()) {
                console.log('🧹 Closing device...');
                bladerf.closeDevice();
            }
        } catch (cleanupError) {
            console.error('❌ Cleanup failed:', cleanupError.message);
        }
        
        process.exit(1);
    }
}

// Run the test
testIQRecording();
