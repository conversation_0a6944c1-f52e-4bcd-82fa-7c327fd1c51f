#!/usr/bin/env node

/**
 * Minimal Video Processor Test
 *
 * This example demonstrates the basic functionality of the minimal video processor
 * implementation without complex parameter handling.
 */

const addon = require('../build/Release/bladerf_addon');

console.log('🚀 Minimal Video Processor Test\n');

try {
    // 1. Test createIQVideoProcessor function
    console.log('1. Testing createIQVideoProcessor function:');
    console.log('   Creating VideoProcessor with WAV stream...');

    const result = addon.createIQVideoProcessor();

    if (result) {
        console.log('   ✅ VideoProcessor created successfully');
        console.log('   📁 Using samples/test_long.wav as data source');
        console.log('   🔄 Loop mode enabled with timing simulation');
    } else {
        console.log('   ❌ Failed to create VideoProcessor');
    }

    console.log('\n2. Testing stopIQVideoProcessor function:');
    const stopResult = addon.stopIQVideoProcessor();

    if (stopResult) {
        console.log('   ✅ VideoProcessor stopped successfully');
    } else {
        console.log('   ⚠️  No VideoProcessor instance was running');
    }

} catch (error) {
    console.error('❌ Error during testing:', error.message);
    process.exit(1);
}

console.log('\n🎉 Minimal video processor test completed!');
console.log('📝 The basic C++ integration is working correctly.');
