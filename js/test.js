const BladeRF = require('./index.js');

console.log('=== BladeRF Node.js C++ Addon Test ===\n');

// Create BladeRF instance
const bladerf = new BladeRF();

async function runTests() {
try {
    // Test 1: Hello World
    console.log('1. Testing Hello World:');
    console.log('   Result:', bladerf.helloWorld());
    console.log('   ✓ Hello World test passed\n');

    // Test 2: Get library version
    console.log('2. Testing Library Version:');
    const libVersion = bladerf.getLibraryVersion();
    console.log('   libbladeRF version:', libVersion);
    console.log('   ✓ Library version test passed\n');

    // Test 2.5: Platform detection
    console.log('2.5. Testing Platform Detection:');
    try {
        const platformInfo = bladerf.getPlatformInfo();
        console.log('   Platform Information:');
        console.log(`     Architecture: ${platformInfo.architecture}`);
        console.log(`     CPU Model: ${platformInfo.cpuModel}`);
        console.log(`     OS: ${platformInfo.osName} ${platformInfo.osVersion}`);
        console.log(`     Is Raspberry Pi: ${platformInfo.isRaspberryPi}`);
        if (platformInfo.isRaspberryPi) {
            console.log(`     Raspberry Pi Version: ${platformInfo.raspberryPiVersion}`);
        }
        console.log(`     Has NEON: ${platformInfo.hasNEON}`);
        console.log(`     Is 64-bit: ${platformInfo.is64Bit}`);

        const threadCount = bladerf.getOptimalThreadCount();
        console.log(`     Optimal Thread Count: ${threadCount}`);

        console.log('   ✓ Platform detection test passed\n');
    } catch (error) {
        console.log('   ⚠️  Platform detection test failed:', error.message, '\n');
    }

    // Test 3: Get device list
    console.log('3. Testing Device List:');
    const devices = bladerf.getDeviceList();
    console.log('   Found', devices.length, 'device(s):');
    devices.forEach((device, index) => {
        console.log(`   Device ${index + 1}:`);
        console.log(`     Serial: ${device.serial}`);
        console.log(`     Backend: ${device.backend}`);
        console.log(`     Instance: ${device.instance}`);
        console.log(`     Available: ${device.available}`);
    });
    
    if (devices.length === 0) {
        console.log('   ⚠️  No BladeRF devices found. Tests will continue without device operations.');
        console.log('   ✓ Device list test passed (no devices found)\n');

        // Test IQ Recording API (without hardware)
        console.log('3. Testing IQ Recording API (no hardware):');
        try {
            // Test that recording fails when device is not open
            bladerf.startIQRecording('test.wav', 5);
            console.log('   ❌ Should have failed (no device open)');
        } catch (error) {
            console.log('   ✅ Correctly failed when device not open');
        }

        // Test recording status functions
        console.log('   Recording status:', bladerf.isRecording());
        console.log('   Recording duration:', bladerf.getRecordingDuration());
        console.log('   Recorded samples:', bladerf.getRecordedSamples());
        console.log('   ✅ IQ recording API test passed\n');

        console.log('=== Test Summary ===');
        console.log('✓ Hello World: PASSED');
        console.log('✓ Library Version: PASSED');
        console.log('✓ Platform Detection: PASSED');
        console.log('✓ Device List: PASSED (no devices)');
        console.log('✓ IQ Recording API: PASSED');
        console.log('⚠️  Device operations: SKIPPED (no devices available)');
        console.log('\nAll available tests completed successfully!');
        console.log('To test device operations and IQ recording, connect a BladeRF device and run again.');
        return;
    }
    
    console.log('   ✓ Device list test passed\n');

    // Test 4: Open device
    console.log('4. Testing Device Open:');
    const openSuccess = bladerf.openDevice();
    if (!openSuccess) {
        throw new Error('Failed to open BladeRF device');
    }
    console.log('   ✓ Device opened successfully\n');

    // Test 5: Check if device is open
    console.log('5. Testing Device Status:');
    const isOpen = bladerf.isDeviceOpen();
    console.log('   Device is open:', isOpen);
    if (!isOpen) {
        throw new Error('Device should be open but reports as closed');
    }
    console.log('   ✓ Device status test passed\n');

    // Test 6: Get device information
    console.log('6. Testing Device Information:');
    try {
        const deviceInfo = bladerf.getDeviceInfo();
        console.log('   Device Serial:', deviceInfo.serial);
        console.log('   Firmware Version:', deviceInfo.firmwareVersion);
        console.log('   FPGA Version:', deviceInfo.fpgaVersion);
        console.log('   Current Config:', deviceInfo.config);
        console.log('   ✓ Device information test passed\n');
    } catch (error) {
        console.log('   ⚠️  Device information test failed:', error.message);
        console.log('   (This may be normal if firmware/FPGA is not loaded)\n');
    }

    // Test 7: Basic configuration test
    console.log('7. Testing Basic Configuration:');
    const configSuccess = bladerf.configure({
        frequency: 915000000,    // 915 MHz
        sampleRate: 1000000,     // 1 MHz
        bandwidth: 1500000,      // 1.5 MHz
        gain: 30                 // 30 dB
    });
    
    if (configSuccess) {
        console.log('   ✓ Basic configuration test passed');
        
        // Get and display current configuration
        try {
            const currentConfig = bladerf.getCurrentConfig();
            console.log('   Current configuration:');
            console.log(`     Frequency: ${currentConfig.frequency} Hz`);
            console.log(`     Sample Rate: ${currentConfig.sampleRate} Hz`);
            console.log(`     Bandwidth: ${currentConfig.bandwidth} Hz`);
            console.log(`     Gain: ${currentConfig.gain} dB`);
        } catch (error) {
            console.log('   ⚠️  Could not read current configuration:', error.message);
        }
    } else {
        console.log('   ⚠️  Basic configuration test failed (may be normal without proper firmware)');
    }
    console.log();

    // Test 8: Test IQ Recording with hardware
    console.log('8. Testing IQ Recording with hardware:');
    try {
        console.log('   📁 Testing short IQ recording (2 seconds)...');
        const testFilename = 'test_hardware_iq.wav';

        if (bladerf.startIQRecording(testFilename, 2)) {
            console.log('   ✅ IQ recording started');

            // Wait for recording to complete
            await new Promise(resolve => {
                const checkComplete = () => {
                    if (!bladerf.isRecording()) {
                        const duration = bladerf.getRecordingDuration();
                        const samples = bladerf.getRecordedSamples();
                        console.log(`   ✅ IQ recording completed: ${duration.toFixed(2)}s, ${samples} samples`);

                        // Check if file was created
                        const fs = require('fs');
                        if (fs.existsSync(testFilename)) {
                            const stats = fs.statSync(testFilename);
                            console.log(`   📁 WAV file created: ${testFilename} (${stats.size} bytes)`);
                        }
                        resolve();
                    } else {
                        setTimeout(checkComplete, 100);
                    }
                };
                checkComplete();
            });
        } else {
            console.log('   ⚠️  IQ recording failed to start (may be normal without proper firmware)');
        }
    } catch (error) {
        console.log('   ⚠️  IQ recording test error:', error.message);
    }
    console.log();

    // Test 9: Perform basic test
    console.log('9. Testing BladeRF Basic Test:');
    try {
        const testSuccess = bladerf.performBasicTest();
        if (testSuccess) {
            console.log('   ✓ BladeRF basic test passed');
        } else {
            console.log('   ⚠️  BladeRF basic test failed (may be normal without proper firmware)');
        }
    } catch (error) {
        console.log('   ⚠️  BladeRF basic test error:', error.message);
    }
    console.log();

    // Test 9: Close device
    console.log('9. Testing Device Close:');
    const closeSuccess = bladerf.closeDevice();
    if (!closeSuccess) {
        throw new Error('Failed to close BladeRF device');
    }
    console.log('   ✓ Device closed successfully\n');

    // Test 10: Verify device is closed
    console.log('10. Testing Device Status After Close:');
    const isClosedNow = bladerf.isDeviceOpen();
    console.log('    Device is open:', isClosedNow);
    if (isClosedNow) {
        throw new Error('Device should be closed but reports as open');
    }
    console.log('    ✓ Device status after close test passed\n');

    console.log('=== Test Summary ===');
    console.log('✓ Hello World: PASSED');
    console.log('✓ Library Version: PASSED');
    console.log('✓ Platform Detection: PASSED');
    console.log('✓ Device List: PASSED');
    console.log('✓ Device Open: PASSED');
    console.log('✓ Device Status: PASSED');
    console.log('✓ Device Information: PASSED');
    console.log('✓ Basic Configuration: PASSED');
    console.log('✓ BladeRF Basic Test: PASSED');
    console.log('✓ Device Close: PASSED');
    console.log('✓ Device Status After Close: PASSED');
    console.log('\nAll tests completed successfully! 🎉');

} catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    
    // Try to close device if it's open
    try {
        if (bladerf.isDeviceOpen()) {
            console.log('\nAttempting to close device...');
            bladerf.closeDevice();
            console.log('Device closed.');
        }
    } catch (closeError) {
        console.error('Failed to close device:', closeError.message);
    }

    process.exit(1);
}
}

// Run the tests
runTests();
