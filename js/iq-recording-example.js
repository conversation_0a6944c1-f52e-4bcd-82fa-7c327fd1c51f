#!/usr/bin/env node

/**
 * BladeRF IQ Recording Example
 * 
 * This example demonstrates how to record IQ data from a BladeRF device
 * and save it as a WAV file for further analysis.
 */

const BladeRF = require('./index.js');

async function recordIQExample() {
    const bladerf = new BladeRF();
    
    try {
        console.log('🎙️  BladeRF IQ Recording Example\n');
        
        // Check for devices
        const devices = bladerf.getDeviceList();
        console.log(`📡 Found ${devices.length} BladeRF device(s)`);
        
        if (devices.length === 0) {
            console.log('❌ No BladeRF devices found. Please connect a device and try again.');
            return;
        }
        
        // Open device
        console.log('🔌 Opening BladeRF device...');
        if (!bladerf.openDevice()) {
            throw new Error('Failed to open BladeRF device');
        }
        console.log('✅ Device opened successfully');
        
        // Configure for FM radio reception (example)
        console.log('\n⚙️  Configuring device for FM radio reception...');
        const config = {
            frequency: 101100000,    // 101.1 MHz FM station
            sampleRate: 2000000,     // 2 MHz sample rate
            bandwidth: 1500000,      // 1.5 MHz bandwidth
            gain: 40                 // 40 dB gain
        };
        
        if (bladerf.configure(config)) {
            console.log('✅ Device configured successfully');
            const currentConfig = bladerf.getCurrentConfig();
            console.log(`   📡 Frequency: ${(currentConfig.frequency / 1e6).toFixed(1)} MHz`);
            console.log(`   📊 Sample Rate: ${(currentConfig.sampleRate / 1e6).toFixed(1)} MHz`);
            console.log(`   📏 Bandwidth: ${(currentConfig.bandwidth / 1e6).toFixed(1)} MHz`);
            console.log(`   🔊 Gain: ${currentConfig.gain} dB`);
        } else {
            console.log('⚠️  Device configuration failed, continuing with defaults...');
        }
        
        // Record IQ data
        console.log('\n🎙️  Recording IQ data...');
        console.log('   📁 Filename: fm_radio_iq.wav');
        console.log('   ⏱️  Duration: 10 seconds');
        console.log('   📊 Format: 16-bit stereo WAV (I=left, Q=right)');
        
        const result = await bladerf.recordIQ('fm_radio_iq.wav', 10);
        
        console.log('\n✅ Recording completed!');
        console.log(`   📊 Duration: ${result.duration.toFixed(2)} seconds`);
        console.log(`   📈 Samples: ${result.samples.toLocaleString()}`);
        console.log(`   📡 Sample Rate: ${(result.sampleRate / 1e6).toFixed(1)} MHz`);
        console.log(`   📁 File: ${result.filename}`);
        
        // Calculate file size
        const fs = require('fs');
        if (fs.existsSync(result.filename)) {
            const stats = fs.statSync(result.filename);
            const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
            console.log(`   💾 File Size: ${sizeMB} MB`);
        }
        
        console.log('\n💡 Next steps:');
        console.log('   1. Open fm_radio_iq.wav in GNU Radio for signal processing');
        console.log('   2. Import into MATLAB/Python for custom analysis');
        console.log('   3. Load in Audacity to visualize I/Q data as stereo audio');
        console.log('   4. Use with SDR software for demodulation');
        
        // Example: Record multiple short samples
        console.log('\n🎯 Recording multiple short samples...');
        const frequencies = [88100000, 95500000, 107900000]; // Different FM stations
        
        for (let i = 0; i < frequencies.length; i++) {
            const freq = frequencies[i];
            console.log(`\n📡 Recording ${(freq / 1e6).toFixed(1)} MHz...`);
            
            // Set frequency
            if (bladerf.setFrequency(freq)) {
                const filename = `fm_${(freq / 1e6).toFixed(1)}mhz.wav`;
                const shortResult = await bladerf.recordIQ(filename, 3); // 3 seconds
                console.log(`   ✅ Recorded ${shortResult.samples.toLocaleString()} samples to ${filename}`);
            } else {
                console.log(`   ❌ Failed to set frequency to ${(freq / 1e6).toFixed(1)} MHz`);
            }
        }
        
        // Close device
        console.log('\n🔌 Closing device...');
        bladerf.closeDevice();
        console.log('✅ Device closed');
        
        console.log('\n🎉 IQ recording example completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Error:', error.message);
        
        // Cleanup
        try {
            if (bladerf.isRecording()) {
                console.log('🛑 Stopping recording...');
                bladerf.stopIQRecording();
            }
            if (bladerf.isDeviceOpen()) {
                console.log('🔌 Closing device...');
                bladerf.closeDevice();
            }
        } catch (cleanupError) {
            console.error('❌ Cleanup error:', cleanupError.message);
        }
        
        process.exit(1);
    }
}

// Run the example
recordIQExample();
