#!/bin/bash

# macOS-specific cross-compilation script using available tools
# Works with LLVM/Clang and existing toolchains

set -e

echo "🍎 BladeRF Video - macOS Cross-Compilation"
echo "========================================="

# Parse command line arguments
TARGET_ARCH="arm64"
RASPBERRY_PI_VERSION="5"

while [[ $# -gt 0 ]]; do
    case $1 in
        --arch)
            TARGET_ARCH="$2"
            shift 2
            ;;
        --rpi)
            RASPBERRY_PI_VERSION="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --arch <arch>     Target architecture (arm64, arm) [default: arm64]"
            echo "  --rpi <version>   Raspberry Pi version (3, 4, 5) [default: 5]"
            echo "  --help           Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                           # Build for Raspberry Pi 5 (ARM64)"
            echo "  $0 --rpi 4                  # Build for Raspberry Pi 4 (ARM64)"
            echo "  $0 --rpi 3 --arch arm       # Build for Raspberry Pi 3 (ARM32)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "📋 Configuration:"
echo "   Target Architecture: $TARGET_ARCH"
echo "   Raspberry Pi Version: $RASPBERRY_PI_VERSION"

# Set CPU optimization based on Raspberry Pi version
case $RASPBERRY_PI_VERSION in
    3)
        CPU_TARGET="cortex-a53"
        ;;
    4)
        CPU_TARGET="cortex-a72"
        ;;
    5)
        CPU_TARGET="cortex-a76"
        ;;
    *)
        echo "❌ Unsupported Raspberry Pi version: $RASPBERRY_PI_VERSION"
        exit 1
        ;;
esac

echo "   CPU Target: $CPU_TARGET"

# Check available compilers
echo ""
echo "🔍 Checking available compilers..."

# Option 1: Try LLVM/Clang with cross-compilation target
if command -v clang &> /dev/null && command -v clang++ &> /dev/null; then
    echo "   ✅ Found: clang/clang++"
    
    if [[ "$TARGET_ARCH" == "arm64" ]]; then
        TARGET_TRIPLE="aarch64-unknown-linux-gnu"
        CC="clang --target=$TARGET_TRIPLE"
        CXX="clang++ --target=$TARGET_TRIPLE"
        COMPILER_TYPE="clang"
    elif [[ "$TARGET_ARCH" == "arm" ]]; then
        TARGET_TRIPLE="arm-unknown-linux-gnueabihf"
        CC="clang --target=$TARGET_TRIPLE"
        CXX="clang++ --target=$TARGET_TRIPLE"
        COMPILER_TYPE="clang"
    fi
    
    echo "   Using Clang with target: $TARGET_TRIPLE"
    
# Option 2: Try existing aarch64-elf-gcc (for ARM64 only)
elif [[ "$TARGET_ARCH" == "arm64" ]] && command -v aarch64-elf-gcc &> /dev/null && command -v aarch64-elf-g++ &> /dev/null; then
    echo "   ✅ Found: aarch64-elf-gcc/aarch64-elf-g++"
    CC="aarch64-elf-gcc"
    CXX="aarch64-elf-g++"
    COMPILER_TYPE="elf-gcc"
    
    echo "   ⚠️  Note: Using ELF toolchain - may need additional configuration"
    
else
    echo ""
    echo "❌ No suitable cross-compiler found for $TARGET_ARCH"
    echo ""
    echo "💡 Available options:"
    echo ""
    echo "1. 🐳 Use Docker cross-compilation (recommended):"
    echo "   ./docker-cross-compile.sh --rpi $RASPBERRY_PI_VERSION --arch $TARGET_ARCH"
    echo ""
    echo "2. 🍎 Install LLVM for better cross-compilation:"
    echo "   brew install llvm"
    echo "   export PATH=\"/opt/homebrew/opt/llvm/bin:\$PATH\""
    echo ""
    echo "3. 🎯 Build directly on Raspberry Pi (easiest):"
    echo "   scp -r . <EMAIL>:~/bladerf-video/"
    echo "   ssh <EMAIL> 'cd ~/bladerf-video && ./install-rpi.sh'"
    echo ""
    echo "4. 📦 Use pre-built binaries (if available)"
    exit 1
fi

# Set up environment variables
export CC="$CC"
export CXX="$CXX"
export AR="ar"
export STRIP="strip"
export LINK="$CXX"

# Set node-gyp specific variables
export CC_target="$CC"
export CXX_target="$CXX"
export AR_target="$AR"
export STRIP_target="$STRIP"
export LINK_target="$LINK"

# Set target architecture for node-gyp
export target_arch="$TARGET_ARCH"
export target_platform="linux"

# Set CPU-specific flags
COMMON_FLAGS="-mcpu=$CPU_TARGET -mtune=$CPU_TARGET -O3"

if [[ "$COMPILER_TYPE" == "clang" ]]; then
    # Clang-specific flags
    export CFLAGS="$COMMON_FLAGS -fPIC"
    export CXXFLAGS="$COMMON_FLAGS -fPIC -std=c++17"
    
    if [[ "$TARGET_ARCH" == "arm" ]]; then
        export CFLAGS="$CFLAGS -mfloat-abi=hard -mfpu=neon-fp-armv8"
        export CXXFLAGS="$CXXFLAGS -mfloat-abi=hard -mfpu=neon-fp-armv8"
    fi
    
    # Add sysroot if available (helps with linking)
    if [[ -d "/opt/homebrew/Cellar/aarch64-elf-gcc" ]]; then
        SYSROOT_PATH=$(find /opt/homebrew/Cellar/aarch64-elf-gcc -name "aarch64-elf" -type d | head -n1)
        if [[ -n "$SYSROOT_PATH" ]]; then
            export CFLAGS="$CFLAGS --sysroot=$SYSROOT_PATH"
            export CXXFLAGS="$CXXFLAGS --sysroot=$SYSROOT_PATH"
        fi
    fi
    
elif [[ "$COMPILER_TYPE" == "elf-gcc" ]]; then
    # ELF GCC flags (may need adjustment for Linux userspace)
    export CFLAGS="$COMMON_FLAGS -fPIC -nostdlib"
    export CXXFLAGS="$COMMON_FLAGS -fPIC -nostdlib -std=c++17"
    
    echo "   ⚠️  Warning: ELF toolchain may not work for Linux userspace applications"
    echo "   Consider using Docker cross-compilation instead"
fi

echo ""
echo "🔧 Environment configured:"
echo "   CC=$CC"
echo "   CXX=$CXX"
echo "   CFLAGS=$CFLAGS"
echo "   CXXFLAGS=$CXXFLAGS"

# Clean previous builds
echo ""
echo "🧹 Cleaning previous builds..."
npm run clean || true
rm -rf build/ || true

# Note about libbladeRF
echo ""
echo "⚠️  Cross-compilation limitations:"
echo "   - libbladeRF headers/libraries for target architecture needed"
echo "   - May require manual library path configuration"
echo "   - Consider building directly on target device for best results"

# Try to configure and build
echo ""
echo "⚙️  Configuring build..."
if ! npx node-gyp configure --target_arch="$TARGET_ARCH" --target_platform="linux"; then
    echo ""
    echo "❌ Configuration failed"
    echo "💡 This is likely due to missing target libraries"
    echo "   Recommendation: Use Docker cross-compilation or build on target device"
    exit 1
fi

echo ""
echo "🔨 Building for $TARGET_ARCH..."
if npx node-gyp build --target_arch="$TARGET_ARCH" --target_platform="linux"; then
    # Check if build was successful
    if [[ -f "build/Release/bladerf_addon.node" ]]; then
        echo ""
        echo "✅ Cross-compilation successful!"
        echo "📁 Output: build/Release/bladerf_addon.node"
        
        # Show file info
        echo ""
        echo "📋 Binary information:"
        file build/Release/bladerf_addon.node
        
        echo ""
        echo "💡 To deploy:"
        echo "   1. Copy build/Release/bladerf_addon.node to your Raspberry Pi"
        echo "   2. Copy package.json, index.js, and other JS files"
        echo "   3. Install libbladeRF on target: sudo apt-get install libbladerf2"
        echo "   4. Run 'npm test' on the target device"
        echo ""
        echo "   Example deployment:"
        echo "   scp -r . <EMAIL>:~/bladerf-video/"
        echo "   ssh <EMAIL> 'cd ~/bladerf-video && sudo apt-get install libbladerf2 && npm test'"
    else
        echo ""
        echo "❌ Build completed but no output file found"
        exit 1
    fi
else
    echo ""
    echo "❌ Cross-compilation failed!"
    echo ""
    echo "💡 Recommended alternatives:"
    echo "   1. Use Docker: ./docker-cross-compile.sh --rpi $RASPBERRY_PI_VERSION --arch $TARGET_ARCH"
    echo "   2. Build on target: scp -r . <EMAIL>:~/bladerf-video/ && ssh <EMAIL> 'cd ~/bladerf-video && ./install-rpi.sh'"
    exit 1
fi

echo ""
echo "🎉 macOS cross-compilation completed!"
