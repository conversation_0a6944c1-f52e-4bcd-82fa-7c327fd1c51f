#!/bin/bash

# Docker-based cross-compilation for ARM targets
# This works reliably on macOS, Linux, and Windows with Docker

set -e

echo "🐳 BladeRF Video - Docker Cross-Compilation"
echo "=========================================="

# Parse command line arguments
TARGET_ARCH="arm64"
RASPBERRY_PI_VERSION="5"
DOCKER_IMAGE="ubuntu:22.04"
CONTAINER_NAME="bladerf-cross-compile"

while [[ $# -gt 0 ]]; do
    case $1 in
        --arch)
            TARGET_ARCH="$2"
            shift 2
            ;;
        --rpi)
            RASPBERRY_PI_VERSION="$2"
            shift 2
            ;;
        --image)
            DOCKER_IMAGE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --arch <arch>     Target architecture (arm64, arm) [default: arm64]"
            echo "  --rpi <version>   Raspberry Pi version (3, 4, 5) [default: 5]"
            echo "  --image <image>   Docker base image [default: ubuntu:22.04]"
            echo "  --help           Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                           # Build for Raspberry Pi 5 (ARM64)"
            echo "  $0 --rpi 4                  # Build for Raspberry Pi 4 (ARM64)"
            echo "  $0 --rpi 3 --arch arm       # Build for Raspberry Pi 3 (ARM32)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "📋 Configuration:"
echo "   Target Architecture: $TARGET_ARCH"
echo "   Raspberry Pi Version: $RASPBERRY_PI_VERSION"
echo "   Docker Image: $DOCKER_IMAGE"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo ""
    echo "❌ Docker is not installed or not in PATH"
    echo ""
    echo "💡 Install Docker:"
    echo "   macOS: brew install --cask docker"
    echo "   Linux: sudo apt-get install docker.io"
    echo "   Windows: Download Docker Desktop from docker.com"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    echo ""
    echo "❌ Docker daemon is not running"
    echo "💡 Start Docker Desktop or run: sudo systemctl start docker"
    exit 1
fi

echo "   ✅ Docker is available"

# Set up cross-compilation packages
if [[ "$TARGET_ARCH" == "arm64" ]]; then
    CROSS_PACKAGES="gcc-aarch64-linux-gnu g++-aarch64-linux-gnu"
    CROSS_PREFIX="aarch64-linux-gnu-"
elif [[ "$TARGET_ARCH" == "arm" ]]; then
    CROSS_PACKAGES="gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf"
    CROSS_PREFIX="arm-linux-gnueabihf-"
else
    echo "❌ Unsupported target architecture: $TARGET_ARCH"
    exit 1
fi

# Set CPU optimization based on Raspberry Pi version
case $RASPBERRY_PI_VERSION in
    3)
        CPU_TARGET="cortex-a53"
        ;;
    4)
        CPU_TARGET="cortex-a72"
        ;;
    5)
        CPU_TARGET="cortex-a76"
        ;;
    *)
        echo "❌ Unsupported Raspberry Pi version: $RASPBERRY_PI_VERSION"
        exit 1
        ;;
esac

echo "   Cross-compilation packages: $CROSS_PACKAGES"
echo "   CPU Target: $CPU_TARGET"

# Create Dockerfile for cross-compilation
echo ""
echo "🔧 Creating cross-compilation environment..."

cat > Dockerfile.cross <<EOF
FROM $DOCKER_IMAGE

# Install build dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    build-essential \\
    cmake \\
    git \\
    pkg-config \\
    python3 \\
    python3-dev \\
    libusb-1.0-0-dev \\
    libcurl4-openssl-dev \\
    libncurses5-dev \\
    libedit-dev \\
    $CROSS_PACKAGES \\
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 18
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \\
    && apt-get install -y nodejs

# Install libbladeRF headers and try to get libraries
RUN apt-get update && apt-get install -y libbladerf-dev libbladerf2 || \\
    (git clone https://github.com/Nuand/bladeRF.git /tmp/bladeRF \\
    && cd /tmp/bladeRF/host \\
    && mkdir build && cd build \\
    && cmake -DCMAKE_BUILD_TYPE=Release \\
             -DCMAKE_INSTALL_PREFIX=/usr/local \\
             -DENABLE_BACKEND_LIBUSB=ON \\
             -DENABLE_BACKEND_LINUX=ON \\
             -DBUILD_DOCUMENTATION=OFF \\
             ../ \\
    && make -j\$(nproc) libbladerf \\
    && make install \\
    && ldconfig \\
    && rm -rf /tmp/bladeRF) \\
    && rm -rf /var/lib/apt/lists/*

# Set up environment for cross-compilation
ENV CC=${CROSS_PREFIX}gcc
ENV CXX=${CROSS_PREFIX}g++
ENV AR=${CROSS_PREFIX}ar
ENV STRIP=${CROSS_PREFIX}strip
ENV LINK=${CROSS_PREFIX}g++
ENV CC_target=\$CC
ENV CXX_target=\$CXX
ENV AR_target=\$AR
ENV STRIP_target=\$STRIP
ENV LINK_target=\$LINK
ENV target_arch=$TARGET_ARCH
ENV target_platform=linux

# Set CPU-specific optimization flags
ENV CFLAGS="-mcpu=$CPU_TARGET -mtune=$CPU_TARGET -O3"
ENV CXXFLAGS="-mcpu=$CPU_TARGET -mtune=$CPU_TARGET -O3 -std=c++17"

WORKDIR /workspace
EOF

# Add ARM32-specific flags
if [[ "$TARGET_ARCH" == "arm" ]]; then
    cat >> Dockerfile.cross <<EOF

# Add ARM32-specific flags
ENV CFLAGS="\$CFLAGS -mfpu=neon-fp-armv8 -mfloat-abi=hard"
ENV CXXFLAGS="\$CXXFLAGS -mfpu=neon-fp-armv8 -mfloat-abi=hard"
EOF
fi

# Build Docker image
echo ""
echo "🔨 Building cross-compilation Docker image..."
docker build -f Dockerfile.cross -t bladerf-cross-$TARGET_ARCH .

# Clean up Dockerfile
rm -f Dockerfile.cross

# Run cross-compilation in container
echo ""
echo "🚀 Running cross-compilation..."

# Clean previous builds
rm -rf build/ node_modules/ || true

docker run --rm \
    -v "$(pwd):/workspace" \
    -w /workspace \
    bladerf-cross-$TARGET_ARCH \
    bash -c "
        echo '🔧 Installing npm dependencies...'
        npm install
        
        echo '⚙️ Configuring build...'
        npx node-gyp configure --target_arch=$TARGET_ARCH --target_platform=linux
        
        echo '🔨 Building addon...'
        npx node-gyp build --target_arch=$TARGET_ARCH --target_platform=linux
        
        echo '📋 Build information:'
        file build/Release/bladerf_addon.node
        ls -la build/Release/bladerf_addon.node
    "

# Check if build was successful
if [[ -f "build/Release/bladerf_addon.node" ]]; then
    echo ""
    echo "✅ Cross-compilation successful!"
    echo "📁 Output: build/Release/bladerf_addon.node"
    
    # Show file info
    echo ""
    echo "📋 Binary information:"
    file build/Release/bladerf_addon.node
    
    echo ""
    echo "💡 To deploy to Raspberry Pi:"
    echo "   1. Copy the entire project directory to your Raspberry Pi"
    echo "   2. The compiled addon should work without rebuilding"
    echo "   3. Run 'npm test' on the target device to verify"
    echo ""
    echo "   Example deployment:"
    echo "   scp -r . <EMAIL>:~/bladerf-video/"
    echo "   ssh <EMAIL> 'cd ~/bladerf-video && npm test'"
else
    echo ""
    echo "❌ Cross-compilation failed!"
    echo "   Check the build output above for errors"
    exit 1
fi

echo ""
echo "🎉 Docker cross-compilation completed successfully!"
