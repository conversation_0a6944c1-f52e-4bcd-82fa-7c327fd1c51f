#!/bin/bash

# Cross-compilation script for ARM targets
# This script helps build the addon for ARM from x86_64 development machines

set -e

echo "🔧 BladeRF Video - ARM Cross-Compilation Script"
echo "=============================================="

# Parse command line arguments
TARGET_ARCH="arm64"
TARGET_PLATFORM="linux"
RASPBERRY_PI_VERSION="5"

while [[ $# -gt 0 ]]; do
    case $1 in
        --arch)
            TARGET_ARCH="$2"
            shift 2
            ;;
        --platform)
            TARGET_PLATFORM="$2"
            shift 2
            ;;
        --rpi)
            RASPBERRY_PI_VERSION="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --arch <arch>     Target architecture (arm64, arm) [default: arm64]"
            echo "  --platform <os>   Target platform (linux) [default: linux]"
            echo "  --rpi <version>   Raspberry Pi version (3, 4, 5) [default: 5]"
            echo "  --help           Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                           # Build for Raspberry Pi 5 (ARM64)"
            echo "  $0 --rpi 4                  # Build for Raspberry Pi 4 (ARM64)"
            echo "  $0 --rpi 3 --arch arm       # Build for Raspberry Pi 3 (ARM32)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "📋 Configuration:"
echo "   Target Architecture: $TARGET_ARCH"
echo "   Target Platform: $TARGET_PLATFORM"
echo "   Raspberry Pi Version: $RASPBERRY_PI_VERSION"

# Set CPU optimization based on Raspberry Pi version
case $RASPBERRY_PI_VERSION in
    3)
        CPU_TARGET="cortex-a53"
        if [[ "$TARGET_ARCH" == "arm64" ]]; then
            echo "⚠️  Note: Raspberry Pi 3 typically uses 32-bit ARM. Consider --arch arm"
        fi
        ;;
    4)
        CPU_TARGET="cortex-a72"
        ;;
    5)
        CPU_TARGET="cortex-a76"
        ;;
    *)
        echo "❌ Unsupported Raspberry Pi version: $RASPBERRY_PI_VERSION"
        echo "   Supported versions: 3, 4, 5"
        exit 1
        ;;
esac

echo "   CPU Target: $CPU_TARGET"

# Check if cross-compilation tools are available
if [[ "$TARGET_ARCH" == "arm64" ]]; then
    # Try different toolchain prefixes in order of preference
    POSSIBLE_PREFIXES=("aarch64-linux-gnu-" "aarch64-unknown-linux-gnu-" "aarch64-elf-")
    CROSS_COMPILE=""

    for prefix in "${POSSIBLE_PREFIXES[@]}"; do
        if command -v "${prefix}gcc" &> /dev/null && command -v "${prefix}g++" &> /dev/null; then
            CROSS_COMPILE="$prefix"
            break
        fi
    done

    if [[ -z "$CROSS_COMPILE" ]]; then
        echo "❌ No suitable ARM64 cross-compiler found"
        echo "   Tried: ${POSSIBLE_PREFIXES[*]}"
        exit 1
    fi

    REQUIRED_TOOLS=("${CROSS_COMPILE}gcc" "${CROSS_COMPILE}g++")
elif [[ "$TARGET_ARCH" == "arm" ]]; then
    # Try different toolchain prefixes for ARM32
    POSSIBLE_PREFIXES=("arm-linux-gnueabihf-" "arm-unknown-linux-gnueabihf-" "arm-elf-")
    CROSS_COMPILE=""

    for prefix in "${POSSIBLE_PREFIXES[@]}"; do
        if command -v "${prefix}gcc" &> /dev/null && command -v "${prefix}g++" &> /dev/null; then
            CROSS_COMPILE="$prefix"
            break
        fi
    done

    if [[ -z "$CROSS_COMPILE" ]]; then
        echo "❌ No suitable ARM32 cross-compiler found"
        echo "   Tried: ${POSSIBLE_PREFIXES[*]}"
        exit 1
    fi

    REQUIRED_TOOLS=("${CROSS_COMPILE}gcc" "${CROSS_COMPILE}g++")
else
    echo "❌ Unsupported target architecture: $TARGET_ARCH"
    exit 1
fi

echo ""
echo "🔍 Checking cross-compilation tools..."
echo "   Using toolchain prefix: $CROSS_COMPILE"

MISSING_TOOLS=()
for tool in "${REQUIRED_TOOLS[@]}"; do
    if ! command -v "$tool" &> /dev/null; then
        MISSING_TOOLS+=("$tool")
    else
        echo "   ✅ Found: $tool"
        # Show version info
        VERSION=$("$tool" --version 2>/dev/null | head -n1 || echo "unknown")
        echo "      Version: $VERSION"
    fi
done

if [[ ${#MISSING_TOOLS[@]} -gt 0 ]]; then
    echo ""
    echo "❌ Missing cross-compilation tools:"
    for tool in "${MISSING_TOOLS[@]}"; do
        echo "   - $tool"
    done
    echo ""
    echo "💡 Install cross-compilation tools:"
    echo ""
    echo "🐧 Linux (Ubuntu/Debian):"
    if [[ "$TARGET_ARCH" == "arm64" ]]; then
        echo "   sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu"
    else
        echo "   sudo apt-get install gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf"
    fi
    echo ""
    echo "🍎 macOS:"
    if [[ "$TARGET_ARCH" == "arm64" ]]; then
        echo "   # Option 1: Use Docker (recommended)"
        echo "   docker run --rm -v \$(pwd):/workspace -w /workspace \\
           ubuntu:22.04 bash -c 'apt update && apt install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu nodejs npm && npm run build'"
        echo ""
        echo "   # Option 2: Try alternative toolchains"
        echo "   brew install llvm"
        echo "   # Then use: export CC=clang CXX=clang++ --target=aarch64-linux-gnu"
        echo ""
        echo "   # Option 3: Build on target device (easiest)"
        echo "   # Copy source to Raspberry Pi and run: npm install && npm run build"
    else
        echo "   # Use Docker (recommended for ARM32)"
        echo "   docker run --rm -v \$(pwd):/workspace -w /workspace \\
           ubuntu:22.04 bash -c 'apt update && apt install -y gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf nodejs npm && npm run build'"
    fi
    echo ""
    echo "🎯 Recommended: Build directly on target Raspberry Pi"
    echo "   scp -r . <EMAIL>:~/bladerf-video/"
    echo "   ssh <EMAIL> 'cd ~/bladerf-video && ./install-rpi.sh'"
    exit 1
fi

# Set environment variables for cross-compilation
export CC="${CROSS_COMPILE}gcc"
export CXX="${CROSS_COMPILE}g++"
export AR="${CROSS_COMPILE}ar"
export STRIP="${CROSS_COMPILE}strip"
export LINK="${CROSS_COMPILE}g++"

# Set node-gyp specific variables
export CC_target="$CC"
export CXX_target="$CXX"
export AR_target="$AR"
export STRIP_target="$STRIP"
export LINK_target="$LINK"

# Set target architecture for node-gyp
export target_arch="$TARGET_ARCH"
export target_platform="$TARGET_PLATFORM"

# Set CPU-specific flags
export CFLAGS="-mcpu=$CPU_TARGET -mtune=$CPU_TARGET -O3"
export CXXFLAGS="-mcpu=$CPU_TARGET -mtune=$CPU_TARGET -O3 -std=c++17"

if [[ "$TARGET_ARCH" == "arm" ]]; then
    export CFLAGS="$CFLAGS -mfpu=neon-fp-armv8 -mfloat-abi=hard"
    export CXXFLAGS="$CXXFLAGS -mfpu=neon-fp-armv8 -mfloat-abi=hard"
fi

echo ""
echo "🔧 Environment configured for cross-compilation:"
echo "   CC=$CC"
echo "   CXX=$CXX"
echo "   CFLAGS=$CFLAGS"
echo "   CXXFLAGS=$CXXFLAGS"

# Clean previous builds
echo ""
echo "🧹 Cleaning previous builds..."
npm run clean || true
rm -rf build/ || true

# Configure and build
echo ""
echo "⚙️  Configuring build..."
npx node-gyp configure --target_arch="$TARGET_ARCH" --target_platform="$TARGET_PLATFORM"

echo ""
echo "🔨 Building for $TARGET_ARCH..."
npx node-gyp build --target_arch="$TARGET_ARCH" --target_platform="$TARGET_PLATFORM"

# Check if build was successful
if [[ -f "build/Release/bladerf_addon.node" ]]; then
    echo ""
    echo "✅ Cross-compilation successful!"
    echo "📁 Output: build/Release/bladerf_addon.node"
    
    # Show file info
    echo ""
    echo "📋 Binary information:"
    file build/Release/bladerf_addon.node
    
    echo ""
    echo "💡 To deploy:"
    echo "   1. Copy build/Release/bladerf_addon.node to your Raspberry Pi"
    echo "   2. Copy package.json, index.js, and other JS files"
    echo "   3. Run 'npm test' on the target device"
else
    echo ""
    echo "❌ Cross-compilation failed!"
    echo "   Check the build output above for errors"
    exit 1
fi

echo ""
echo "🎉 Cross-compilation completed successfully!"
