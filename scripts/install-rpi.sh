#!/bin/bash

# BladeRF Video - Raspberry Pi 5 Installation Script
# This script installs all dependencies and builds the addon for ARM64/ARM architecture

set -e

echo "🍓 BladeRF Video - Raspberry Pi 5 Installation Script"
echo "=================================================="

# Check if running on ARM
ARCH=$(uname -m)
echo "📋 Detected architecture: $ARCH"

if [[ "$ARCH" != "aarch64" && "$ARCH" != "armv7l" && "$ARCH" != "arm64" ]]; then
    echo "⚠️  Warning: This script is optimized for ARM architecture (Raspberry Pi)"
    echo "   Detected: $ARCH"
    echo "   Continuing anyway..."
fi

# Check OS
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "❌ This script is designed for Linux (Raspberry Pi OS)"
    exit 1
fi

echo "✅ Running on Linux ARM - proceeding with installation"

# Update package lists
echo ""
echo "📦 Updating package lists..."
sudo apt-get update

# Install build dependencies
echo ""
echo "🔧 Installing build dependencies..."
sudo apt-get install -y \
    build-essential \
    cmake \
    git \
    pkg-config \
    libusb-1.0-0-dev \
    libusb-1.0-0 \
    libncurses5-dev \
    libedit-dev \
    wget \
    curl \
    python3 \
    python3-dev \
    python3-pip

# Install Node.js if not present or version is too old
echo ""
echo "📋 Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "📥 Node.js not found, installing..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
else
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 14 ]; then
        echo "⚠️  Node.js version is too old ($(node -v)), updating..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    else
        echo "✅ Node.js $(node -v) is compatible"
    fi
fi

# Install BladeRF libraries
echo ""
echo "📡 Installing BladeRF libraries..."

# Check if libbladeRF is available in repositories
if apt-cache show libbladerf-dev &> /dev/null; then
    echo "📦 Installing libbladeRF from repositories..."
    sudo apt-get install -y libbladerf2 libbladerf-dev bladerf
else
    echo "🔨 Building libbladeRF from source..."
    
    # Create temporary build directory
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Clone BladeRF repository
    echo "📥 Cloning BladeRF repository..."
    git clone https://github.com/Nuand/bladeRF.git
    cd bladeRF
    
    # Build and install
    echo "🔨 Building libbladeRF..."
    cd host
    mkdir build
    cd build
    
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DCMAKE_INSTALL_PREFIX=/usr/local \
          -DINSTALL_UDEV_RULES=ON \
          -DBLADERF_GROUP=plugdev \
          ../
    
    make -j$(nproc)
    sudo make install
    sudo ldconfig
    
    # Clean up
    cd /
    rm -rf "$TEMP_DIR"
    
    echo "✅ libbladeRF built and installed successfully"
fi

# Add user to plugdev group for device access
echo ""
echo "👤 Setting up device permissions..."
if ! groups $USER | grep -q plugdev; then
    echo "📝 Adding user $USER to plugdev group..."
    sudo usermod -a -G plugdev $USER
    echo "⚠️  You will need to log out and log back in for group changes to take effect"
else
    echo "✅ User $USER is already in plugdev group"
fi

# Return to project directory
cd "$(dirname "$0")"

# Install npm dependencies and build
echo ""
echo "📦 Installing npm dependencies..."
npm install

echo ""
echo "🔨 Building C++ addon for ARM..."
npm run build

echo ""
echo "🧪 Running tests..."
npm test

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Summary:"
echo "   ✅ Build dependencies installed"
echo "   ✅ libbladeRF installed"
echo "   ✅ Device permissions configured"
echo "   ✅ C++ addon built for ARM"
echo "   ✅ Tests passed"
echo ""
echo "💡 Next steps:"
echo "   1. Connect your BladeRF device"
echo "   2. Run 'npm run example' to test with hardware"
echo "   3. If you added user to plugdev group, log out and back in"
echo ""
echo "🚀 Ready to use BladeRF with Node.js on Raspberry Pi!"
