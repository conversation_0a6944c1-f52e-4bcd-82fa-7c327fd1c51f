# Generate C++ code for video sync encoder wheels
cpp_code = '''#pragma once
#include <vector>
#include <string>
#include <stdexcept>

// Enum for sync pulse types
enum class SyncPulseType {
SHORT_EQUALIZING,    // ~2.3 µs
LONG_VERTICAL,       // ~27 µs  
NORMAL_HORIZONTAL,   // ~4.7 µs
HALF_LINE_OFFSET     // ~4.7 µs but with interlace offset
};

// Structure representing a single "tooth" on the encoder wheel
struct SyncTooth {
SyncPulseType type;
double duration_us;  // Duration in microseconds
std::string name;    // Human-readable name for debugging
    
    SyncTooth(SyncPulseType t, double dur, const std::string& n) 
        : type(t), duration_us(dur), name(n) {}
};

// Abstract interface for encoder wheels
class IEncoderWheel {
public:
virtual ~IEncoderWheel() = default;

    // Get current active tooth
    virtual const SyncTooth& getCurrentTooth() const = 0;
    
    // Advance to next tooth (idx++)
    virtual void tick() = 0;
    
    // Reset to beginning
    virtual void reset() = 0;
    
    // Get total number of teeth
    virtual size_t getToothCount() const = 0;
    
    // Get current position
    virtual size_t getCurrentIndex() const = 0;
    
    // Get tooth by index
    virtual const SyncTooth& getToothAt(size_t index) const = 0;
    
    // Check if we've completed a full rotation
    virtual bool isAtStart() const = 0;
};

// Base implementation for encoder wheels
class EncoderWheelBase : public IEncoderWheel {
protected:
std::vector<SyncTooth> teeth;
size_t current_index;

public:
EncoderWheelBase() : current_index(0) {}

    const SyncTooth& getCurrentTooth() const override {
        if (teeth.empty()) {
            throw std::runtime_error("Encoder wheel is empty");
        }
        return teeth[current_index];
    }
    
    void tick() override {
        if (!teeth.empty()) {
            current_index = (current_index + 1) % teeth.size();
        }
    }
    
    void reset() override {
        current_index = 0;
    }
    
    size_t getToothCount() const override {
        return teeth.size();
    }
    
    size_t getCurrentIndex() const override {
        return current_index;
    }
    
    const SyncTooth& getToothAt(size_t index) const override {
        if (index >= teeth.size()) {
            throw std::out_of_range("Tooth index out of range");
        }
        return teeth[index];
    }
    
    bool isAtStart() const override {
        return current_index == 0;
    }
};

// NTSC 525/60 interlaced encoder wheel
class NTSCEncoderWheel : public EncoderWheelBase {
public:
NTSCEncoderWheel() {
buildNTSCSequence();
}

private:
void buildNTSCSequence() {
teeth.clear();
teeth.reserve(525);

        // FIELD 1 (odd lines)
        // Pre-equalizing pulses (6 short)
        for (int i = 1; i <= 6; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.3, 
                             "Field1_PreEq_" + std::to_string(i));
        }
        
        // Vertical sync pulses (6 long)
        for (int i = 1; i <= 6; ++i) {
            teeth.emplace_back(SyncPulseType::LONG_VERTICAL, 27.0, 
                             "Field1_VSync_" + std::to_string(i));
        }
        
        // Post-equalizing pulses (6 short)
        for (int i = 1; i <= 6; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.3, 
                             "Field1_PostEq_" + std::to_string(i));
        }
        
        // Normal horizontal sync for active lines (240 lines)
        for (int i = 1; i <= 240; ++i) {
            teeth.emplace_back(SyncPulseType::NORMAL_HORIZONTAL, 4.7, 
                             "Field1_Line_" + std::to_string(i + 21));
        }
        
        // Half-line offset (end of field 1)
        teeth.emplace_back(SyncPulseType::HALF_LINE_OFFSET, 4.7, "Field1_HalfLine");
        
        // FIELD 2 (even lines) - same pattern but offset
        // Pre-equalizing pulses (6 short)
        for (int i = 1; i <= 6; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.3, 
                             "Field2_PreEq_" + std::to_string(i));
        }
        
        // Vertical sync pulses (6 long)
        for (int i = 1; i <= 6; ++i) {
            teeth.emplace_back(SyncPulseType::LONG_VERTICAL, 27.0, 
                             "Field2_VSync_" + std::to_string(i));
        }
        
        // Post-equalizing pulses (6 short)
        for (int i = 1; i <= 6; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.3, 
                             "Field2_PostEq_" + std::to_string(i));
        }
        
        // Normal horizontal sync for active lines (240 lines)
        for (int i = 1; i <= 240; ++i) {
            teeth.emplace_back(SyncPulseType::NORMAL_HORIZONTAL, 4.7, 
                             "Field2_Line_" + std::to_string(i + 283));
        }
        
        // Half-line offset (end of field 2)
        teeth.emplace_back(SyncPulseType::HALF_LINE_OFFSET, 4.7, "Field2_HalfLine");
    }
};

// PAL 625/50 non-interlaced (progressive) encoder wheel
class PALProgressiveEncoderWheel : public EncoderWheelBase {
public:
PALProgressiveEncoderWheel() {
buildPALProgressiveSequence();
}

private:
void buildPALProgressiveSequence() {
teeth.clear();
teeth.reserve(625);

        // Pre-equalizing pulses (5 short)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.35, 
                             "PreEq_" + std::to_string(i));
        }
        
        // Vertical sync pulses (5 long)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::LONG_VERTICAL, 27.0, 
                             "VSync_" + std::to_string(i));
        }
        
        // Post-equalizing pulses (5 short)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.35, 
                             "PostEq_" + std::to_string(i));
        }
        
        // Normal horizontal sync for all remaining lines (610 lines)
        for (int i = 1; i <= 610; ++i) {
            teeth.emplace_back(SyncPulseType::NORMAL_HORIZONTAL, 4.7, 
                             "Line_" + std::to_string(i + 15));
        }
    }
};

// PAL 625/50 interlaced encoder wheel
class PALInterlacedEncoderWheel : public EncoderWheelBase {
public:
PALInterlacedEncoderWheel() {
buildPALInterlacedSequence();
}

private:
void buildPALInterlacedSequence() {
teeth.clear();
teeth.reserve(625);

        // FIELD 1 (odd lines)
        // Pre-equalizing pulses (5 short)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.35, 
                             "Field1_PreEq_" + std::to_string(i));
        }
        
        // Vertical sync pulses (5 long)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::LONG_VERTICAL, 27.0, 
                             "Field1_VSync_" + std::to_string(i));
        }
        
        // Post-equalizing pulses (5 short)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.35, 
                             "Field1_PostEq_" + std::to_string(i));
        }
        
        // Normal horizontal sync for active lines (288 lines)
        for (int i = 1; i <= 288; ++i) {
            teeth.emplace_back(SyncPulseType::NORMAL_HORIZONTAL, 4.7, 
                             "Field1_Line_" + std::to_string(i + 22));
        }
        
        // VBI lines for field 1 (remaining lines before half-line)
        for (int i = 1; i <= 9; ++i) {
            teeth.emplace_back(SyncPulseType::NORMAL_HORIZONTAL, 4.7, 
                             "Field1_VBI_" + std::to_string(i));
        }
        
        // Half-line offset (end of field 1)
        teeth.emplace_back(SyncPulseType::HALF_LINE_OFFSET, 4.7, "Field1_HalfLine");
        
        // FIELD 2 (even lines)
        // Pre-equalizing pulses (5 short)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.35, 
                             "Field2_PreEq_" + std::to_string(i));
        }
        
        // Vertical sync pulses (5 long)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::LONG_VERTICAL, 27.0, 
                             "Field2_VSync_" + std::to_string(i));
        }
        
        // Post-equalizing pulses (5 short)
        for (int i = 1; i <= 5; ++i) {
            teeth.emplace_back(SyncPulseType::SHORT_EQUALIZING, 2.35, 
                             "Field2_PostEq_" + std::to_string(i));
        }
        
        // Normal horizontal sync for active lines (288 lines)
        for (int i = 1; i <= 288; ++i) {
            teeth.emplace_back(SyncPulseType::NORMAL_HORIZONTAL, 4.7, 
                             "Field2_Line_" + std::to_string(i + 335));
        }
        
        // VBI lines for field 2 (remaining lines)
        for (int i = 1; i <= 9; ++i) {
            teeth.emplace_back(SyncPulseType::NORMAL_HORIZONTAL, 4.7, 
                             "Field2_VBI_" + std::to_string(i));
        }
        
        // Half-line offset (end of field 2)
        teeth.emplace_back(SyncPulseType::HALF_LINE_OFFSET, 4.7, "Field2_HalfLine");
    }
};

// Example usage
#include <iostream>

void demonstrateEncoderWheel(IEncoderWheel& wheel, const std::string& name) {
std::cout << "\\n=== " << name << " ===" << std::endl;
std::cout << "Total teeth: " << wheel.getToothCount() << std::endl;

    // Show first 20 teeth
    std::cout << "First 20 teeth:" << std::endl;
    for (size_t i = 0; i < 20 && i < wheel.getToothCount(); ++i) {
        const auto& tooth = wheel.getToothAt(i);
        std::cout << "Tooth " << (i+1) << ": " << tooth.name 
                  << " (" << tooth.duration_us << " µs)" << std::endl;
    }
    
    // Demonstrate tick functionality
    std::cout << "\\nCurrent tooth: " << wheel.getCurrentTooth().name << std::endl;
    wheel.tick();
    std::cout << "After tick: " << wheel.getCurrentTooth().name << std::endl;
    wheel.reset();
    std::cout << "After reset: " << wheel.getCurrentTooth().name << std::endl;
}

int main() {
// Create instances of each encoder wheel
NTSCEncoderWheel ntsc;
PALProgressiveEncoderWheel pal_prog;
PALInterlacedEncoderWheel pal_interlaced;

    // Demonstrate each wheel
    demonstrateEncoderWheel(ntsc, "NTSC 525/60 Interlaced");
    demonstrateEncoderWheel(pal_prog, "PAL 625/50 Progressive");
    demonstrateEncoderWheel(pal_interlaced, "PAL 625/50 Interlaced");
    
    return 0;
}'''

# Save to file
with open('video_sync_encoder.cpp', 'w') as f:
f.write(cpp_code)

print("C++ code generated and saved to 'video_sync_encoder.cpp'")
print("\nKey features:")
print("- Abstract IEncoderWheel interface")
print("- SyncTooth struct with type, duration, and name")
print("- Three implementations: NTSC, PAL Progressive, PAL Interlaced")
print("- getCurrentTooth() and tick() methods as requested")
print("- Vector-based storage with easy access to each tooth")