#include "platform_detect.h"
#include <fstream>
#include <sstream>
#include <thread>
#include <cstring>
#include <unistd.h>
#include <sys/utsname.h>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

namespace PlatformDetect {
    
    std::string readFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            return "";
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }
    
    std::string getArchitecture() {
        struct utsname info;
        if (uname(&info) == 0) {
            return std::string(info.machine);
        }
        
#ifdef __aarch64__
        return "aarch64";
#elif defined(__arm__)
        return "armv7l";
#elif defined(__x86_64__)
        return "x86_64";
#elif defined(__i386__)
        return "i386";
#else
        return "unknown";
#endif
    }
    
    std::string getCPUModel() {
        std::string cpuinfo = readFile("/proc/cpuinfo");
        if (cpuinfo.empty()) {
            return "unknown";
        }
        
        std::istringstream stream(cpuinfo);
        std::string line;
        
        while (std::getline(stream, line)) {
            if (line.find("model name") != std::string::npos ||
                line.find("Model") != std::string::npos) {
                size_t pos = line.find(':');
                if (pos != std::string::npos) {
                    std::string model = line.substr(pos + 1);
                    // Trim whitespace
                    model.erase(0, model.find_first_not_of(" \t"));
                    model.erase(model.find_last_not_of(" \t") + 1);
                    return model;
                }
            }
        }
        
        return "unknown";
    }
    
    bool isRaspberryPi() {
        // Check /proc/device-tree/model
        std::string model = readFile("/proc/device-tree/model");
        if (model.find("Raspberry Pi") != std::string::npos) {
            return true;
        }
        
        // Check /proc/cpuinfo
        std::string cpuinfo = readFile("/proc/cpuinfo");
        if (cpuinfo.find("BCM") != std::string::npos ||
            cpuinfo.find("Raspberry Pi") != std::string::npos) {
            return true;
        }
        
        return false;
    }
    
    int getRaspberryPiVersion() {
        if (!isRaspberryPi()) {
            return 0;
        }
        
        std::string model = readFile("/proc/device-tree/model");
        
        if (model.find("Raspberry Pi 5") != std::string::npos) {
            return 5;
        } else if (model.find("Raspberry Pi 4") != std::string::npos) {
            return 4;
        } else if (model.find("Raspberry Pi 3") != std::string::npos) {
            return 3;
        } else if (model.find("Raspberry Pi 2") != std::string::npos) {
            return 2;
        } else if (model.find("Raspberry Pi") != std::string::npos) {
            return 1;
        }
        
        return 0;
    }
    
    bool hasNEON() {
#ifdef __ARM_NEON
        return true;
#else
        // Check /proc/cpuinfo for NEON support
        std::string cpuinfo = readFile("/proc/cpuinfo");
        return cpuinfo.find("neon") != std::string::npos;
#endif
    }
    
    bool is64Bit() {
        return sizeof(void*) == 8;
    }
    
    int getOptimalThreadCount() {
        int hw_threads = std::thread::hardware_concurrency();
        
        if (hw_threads == 0) {
            // Fallback based on platform
            if (isRaspberryPi()) {
                int version = getRaspberryPiVersion();
                switch (version) {
                    case 5:
                    case 4: return 4;
                    case 3:
                    case 2: return 4;
                    default: return 1;
                }
            }
            return 2; // Conservative default
        }
        
        // For ARM systems, sometimes it's better to use fewer threads
        // to avoid thermal throttling
        if (getArchitecture().find("arm") != std::string::npos ||
            getArchitecture().find("aarch64") != std::string::npos) {
            return std::min(hw_threads, 4);
        }
        
        return hw_threads;
    }
    
    bool shouldUseNEON() {
        return hasNEON() && (getArchitecture().find("arm") != std::string::npos ||
                            getArchitecture().find("aarch64") != std::string::npos);
    }
    
    std::string getOptimizationFlags() {
        std::string flags;
        
        if (isRaspberryPi()) {
            int version = getRaspberryPiVersion();
            switch (version) {
                case 5:
                    flags = "-mcpu=cortex-a76 -mtune=cortex-a76";
                    break;
                case 4:
                    flags = "-mcpu=cortex-a72 -mtune=cortex-a72";
                    break;
                case 3:
                    flags = "-mcpu=cortex-a53 -mtune=cortex-a53";
                    if (!is64Bit()) {
                        flags += " -mfpu=neon-fp-armv8 -mfloat-abi=hard";
                    }
                    break;
                default:
                    flags = "-mcpu=arm1176jzf-s";
                    break;
            }
        } else {
            std::string arch = getArchitecture();
            if (arch == "aarch64" || arch == "arm64") {
                flags = "-mcpu=native -mtune=native";
            } else if (arch.find("arm") != std::string::npos) {
                flags = "-mcpu=native -mtune=native -mfloat-abi=hard";
                if (hasNEON()) {
                    flags += " -mfpu=neon";
                }
            }
        }
        
        if (shouldUseNEON()) {
            flags += " -DUSE_NEON=1";
        }
        
        return flags;
    }
    
    PlatformInfo detectPlatform() {
        PlatformInfo info;
        
        info.architecture = getArchitecture();
        info.cpu_model = getCPUModel();
        info.is_raspberry_pi = isRaspberryPi();
        info.raspberry_pi_version = getRaspberryPiVersion();
        info.has_neon = hasNEON();
        info.is_64bit = is64Bit();
        
        // Get OS information
        struct utsname uname_info;
        if (uname(&uname_info) == 0) {
            info.os_name = std::string(uname_info.sysname);
            info.os_version = std::string(uname_info.release);
        } else {
            info.os_name = "unknown";
            info.os_version = "unknown";
        }
        
        return info;
    }
    
} // namespace PlatformDetect
