#ifndef WAV_WRITER_H
#define WAV_WRITER_H

#include <string>
#include <fstream>
#include <vector>
#include <cstdint>

namespace WavWriter {
    
    struct WavHeader {
        // RIFF Header
        char riff_header[4];        // "RIFF"
        uint32_t wav_size;          // File size - 8
        char wave_header[4];        // "WAVE"
        
        // Format Header
        char fmt_header[4];         // "fmt "
        uint32_t fmt_chunk_size;    // Size of format chunk (16 for PCM)
        uint16_t audio_format;      // Audio format (1 for PCM)
        uint16_t num_channels;      // Number of channels
        uint32_t sample_rate;       // Sample rate
        uint32_t byte_rate;         // Byte rate
        uint16_t sample_alignment;  // Sample alignment
        uint16_t bit_depth;         // Bit depth
        
        // Data Header
        char data_header[4];        // "data"
        uint32_t data_bytes;        // Number of data bytes
    };
    
    class IQWavWriter {
    private:
        std::ofstream file;
        std::string filename;
        uint32_t sample_rate;
        uint32_t samples_written;
        bool is_open;
        
        void writeHeader();
        void updateHeader();
        
    public:
        IQWavWriter();
        ~IQWavWriter();
        
        bool open(const std::string& filename, uint32_t sample_rate);
        bool writeIQSamples(const int16_t* i_samples, const int16_t* q_samples, size_t num_samples);
        bool writeInterleavedIQ(const int16_t* iq_samples, size_t num_samples);
        void close();
        
        uint32_t getSamplesWritten() const { return samples_written; }
        double getDurationSeconds() const { return (double)samples_written / sample_rate; }
        bool isOpen() const { return is_open; }
    };
    
} // namespace WavWriter

#endif // WAV_WRITER_H
