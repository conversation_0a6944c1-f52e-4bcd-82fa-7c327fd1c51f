#include <node.h>
#include <v8.h>
#include "bladerf_wrapper.h"
#include "platform_detect.h"

namespace BladeRFAddon {

using v8::Context;
using v8::Function;
using v8::FunctionCallbackInfo;
using v8::Isolate;
using v8::Local;
using v8::Object;
using v8::String;
using v8::Value;
using v8::Array;
using v8::Number;
using v8::Boolean;

// Hello World function
void HelloWorld(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, "Hello from BladeRF C++ addon!").ToLocalChecked());
}

// Get library version
void GetLibraryVersion(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    std::string version = BladeRFWrapper::getLibraryVersion();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, version.c_str()).ToLocalChecked());
}

// Get device list
void GetDeviceList(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    Local<Context> context = isolate->GetCurrentContext();
    
    std::vector<BladeRFWrapper::DeviceInfo> devices = BladeRFWrapper::getDeviceList();
    Local<Array> result = Array::New(isolate, devices.size());
    
    for (size_t i = 0; i < devices.size(); i++) {
        Local<Object> device = Object::New(isolate);
        device->Set(context, String::NewFromUtf8(isolate, "serial").ToLocalChecked(),
                   String::NewFromUtf8(isolate, devices[i].serial.c_str()).ToLocalChecked()).Check();
        device->Set(context, String::NewFromUtf8(isolate, "backend").ToLocalChecked(),
                   String::NewFromUtf8(isolate, devices[i].backend.c_str()).ToLocalChecked()).Check();
        device->Set(context, String::NewFromUtf8(isolate, "instance").ToLocalChecked(),
                   String::NewFromUtf8(isolate, devices[i].instance.c_str()).ToLocalChecked()).Check();
        device->Set(context, String::NewFromUtf8(isolate, "available").ToLocalChecked(),
                   Boolean::New(isolate, devices[i].available)).Check();
        
        result->Set(context, i, device).Check();
    }
    
    args.GetReturnValue().Set(result);
}

// Open device
void OpenDevice(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    
    std::string device_id = "";
    if (args.Length() > 0 && args[0]->IsString()) {
        v8::String::Utf8Value utf8_value(isolate, args[0]);
        device_id = std::string(*utf8_value);
    }
    
    bool success = BladeRFWrapper::openDevice(device_id);
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Close device
void CloseDevice(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    bool success = BladeRFWrapper::closeDevice();
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Check if device is open
void IsDeviceOpen(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    bool isOpen = BladeRFWrapper::isDeviceOpen();
    args.GetReturnValue().Set(Boolean::New(isolate, isOpen));
}

// Set frequency
void SetFrequency(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    
    if (args.Length() < 1 || !args[0]->IsNumber()) {
        isolate->ThrowException(v8::Exception::TypeError(
            String::NewFromUtf8(isolate, "Frequency must be a number").ToLocalChecked()));
        return;
    }
    
    uint64_t frequency = static_cast<uint64_t>(args[0]->NumberValue(isolate->GetCurrentContext()).FromJust());
    bool success = BladeRFWrapper::setFrequency(frequency);
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Set sample rate
void SetSampleRate(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    
    if (args.Length() < 1 || !args[0]->IsNumber()) {
        isolate->ThrowException(v8::Exception::TypeError(
            String::NewFromUtf8(isolate, "Sample rate must be a number").ToLocalChecked()));
        return;
    }
    
    uint32_t sample_rate = args[0]->Uint32Value(isolate->GetCurrentContext()).FromJust();
    bool success = BladeRFWrapper::setSampleRate(sample_rate);
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Set bandwidth
void SetBandwidth(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    
    if (args.Length() < 1 || !args[0]->IsNumber()) {
        isolate->ThrowException(v8::Exception::TypeError(
            String::NewFromUtf8(isolate, "Bandwidth must be a number").ToLocalChecked()));
        return;
    }
    
    uint32_t bandwidth = args[0]->Uint32Value(isolate->GetCurrentContext()).FromJust();
    bool success = BladeRFWrapper::setBandwidth(bandwidth);
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Set gain
void SetGain(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    
    if (args.Length() < 1 || !args[0]->IsNumber()) {
        isolate->ThrowException(v8::Exception::TypeError(
            String::NewFromUtf8(isolate, "Gain must be a number").ToLocalChecked()));
        return;
    }
    
    int gain = args[0]->Int32Value(isolate->GetCurrentContext()).FromJust();
    bool success = BladeRFWrapper::setGain(gain);
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Get current configuration
void GetCurrentConfig(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    Local<Context> context = isolate->GetCurrentContext();
    
    BladeRFWrapper::DeviceConfig config = BladeRFWrapper::getCurrentConfig();
    Local<Object> result = Object::New(isolate);
    
    result->Set(context, String::NewFromUtf8(isolate, "frequency").ToLocalChecked(),
               Number::New(isolate, config.frequency)).Check();
    result->Set(context, String::NewFromUtf8(isolate, "sampleRate").ToLocalChecked(),
               Number::New(isolate, config.sample_rate)).Check();
    result->Set(context, String::NewFromUtf8(isolate, "bandwidth").ToLocalChecked(),
               Number::New(isolate, config.bandwidth)).Check();
    result->Set(context, String::NewFromUtf8(isolate, "gain").ToLocalChecked(),
               Number::New(isolate, config.gain)).Check();
    
    args.GetReturnValue().Set(result);
}

// Get device serial
void GetDeviceSerial(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    std::string serial = BladeRFWrapper::getDeviceSerial();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, serial.c_str()).ToLocalChecked());
}

// Get firmware version
void GetFirmwareVersion(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    std::string version = BladeRFWrapper::getFirmwareVersion();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, version.c_str()).ToLocalChecked());
}

// Get FPGA version
void GetFPGAVersion(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    std::string version = BladeRFWrapper::getFPGAVersion();
    args.GetReturnValue().Set(String::NewFromUtf8(isolate, version.c_str()).ToLocalChecked());
}

// Perform basic test
void PerformBasicTest(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    bool success = BladeRFWrapper::performBasicTest();
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Get platform information
void GetPlatformInfo(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    Local<Context> context = isolate->GetCurrentContext();

    PlatformDetect::PlatformInfo info = PlatformDetect::detectPlatform();
    Local<Object> result = Object::New(isolate);

    result->Set(context, String::NewFromUtf8(isolate, "architecture").ToLocalChecked(),
               String::NewFromUtf8(isolate, info.architecture.c_str()).ToLocalChecked()).Check();
    result->Set(context, String::NewFromUtf8(isolate, "cpuModel").ToLocalChecked(),
               String::NewFromUtf8(isolate, info.cpu_model.c_str()).ToLocalChecked()).Check();
    result->Set(context, String::NewFromUtf8(isolate, "osName").ToLocalChecked(),
               String::NewFromUtf8(isolate, info.os_name.c_str()).ToLocalChecked()).Check();
    result->Set(context, String::NewFromUtf8(isolate, "osVersion").ToLocalChecked(),
               String::NewFromUtf8(isolate, info.os_version.c_str()).ToLocalChecked()).Check();
    result->Set(context, String::NewFromUtf8(isolate, "isRaspberryPi").ToLocalChecked(),
               Boolean::New(isolate, info.is_raspberry_pi)).Check();
    result->Set(context, String::NewFromUtf8(isolate, "raspberryPiVersion").ToLocalChecked(),
               Number::New(isolate, info.raspberry_pi_version)).Check();
    result->Set(context, String::NewFromUtf8(isolate, "hasNEON").ToLocalChecked(),
               Boolean::New(isolate, info.has_neon)).Check();
    result->Set(context, String::NewFromUtf8(isolate, "is64Bit").ToLocalChecked(),
               Boolean::New(isolate, info.is_64bit)).Check();

    args.GetReturnValue().Set(result);
}

// Get optimal thread count
void GetOptimalThreadCount(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    int threadCount = PlatformDetect::getOptimalThreadCount();
    args.GetReturnValue().Set(Number::New(isolate, threadCount));
}

// Start IQ recording
void StartIQRecording(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();

    if (args.Length() < 2 || !args[0]->IsString() || !args[1]->IsNumber()) {
        isolate->ThrowException(v8::Exception::TypeError(
            String::NewFromUtf8(isolate, "Usage: startIQRecording(filename, maxDurationSeconds)").ToLocalChecked()));
        return;
    }

    v8::String::Utf8Value filename_utf8(isolate, args[0]);
    std::string filename(*filename_utf8);
    double max_duration = args[1]->NumberValue(isolate->GetCurrentContext()).FromJust();

    bool success = BladeRFWrapper::startIQRecording(filename, max_duration);
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Stop IQ recording
void StopIQRecording(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    bool success = BladeRFWrapper::stopIQRecording();
    args.GetReturnValue().Set(Boolean::New(isolate, success));
}

// Check if recording
void IsRecording(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    bool recording = BladeRFWrapper::isRecording();
    args.GetReturnValue().Set(Boolean::New(isolate, recording));
}

// Get recording duration
void GetRecordingDuration(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    double duration = BladeRFWrapper::getRecordingDuration();
    args.GetReturnValue().Set(Number::New(isolate, duration));
}

// Get recorded samples count
void GetRecordedSamples(const FunctionCallbackInfo<Value>& args) {
    Isolate* isolate = args.GetIsolate();
    uint32_t samples = BladeRFWrapper::getRecordedSamples();
    args.GetReturnValue().Set(Number::New(isolate, samples));
}

// Initialize the addon
void Initialize(Local<Object> exports) {
    NODE_SET_METHOD(exports, "helloWorld", HelloWorld);
    NODE_SET_METHOD(exports, "getLibraryVersion", GetLibraryVersion);
    NODE_SET_METHOD(exports, "getDeviceList", GetDeviceList);
    NODE_SET_METHOD(exports, "openDevice", OpenDevice);
    NODE_SET_METHOD(exports, "closeDevice", CloseDevice);
    NODE_SET_METHOD(exports, "isDeviceOpen", IsDeviceOpen);
    NODE_SET_METHOD(exports, "setFrequency", SetFrequency);
    NODE_SET_METHOD(exports, "setSampleRate", SetSampleRate);
    NODE_SET_METHOD(exports, "setBandwidth", SetBandwidth);
    NODE_SET_METHOD(exports, "setGain", SetGain);
    NODE_SET_METHOD(exports, "getCurrentConfig", GetCurrentConfig);
    NODE_SET_METHOD(exports, "getDeviceSerial", GetDeviceSerial);
    NODE_SET_METHOD(exports, "getFirmwareVersion", GetFirmwareVersion);
    NODE_SET_METHOD(exports, "getFPGAVersion", GetFPGAVersion);
    NODE_SET_METHOD(exports, "performBasicTest", PerformBasicTest);
    NODE_SET_METHOD(exports, "getPlatformInfo", GetPlatformInfo);
    NODE_SET_METHOD(exports, "getOptimalThreadCount", GetOptimalThreadCount);
    NODE_SET_METHOD(exports, "startIQRecording", StartIQRecording);
    NODE_SET_METHOD(exports, "stopIQRecording", StopIQRecording);
    NODE_SET_METHOD(exports, "isRecording", IsRecording);
    NODE_SET_METHOD(exports, "getRecordingDuration", GetRecordingDuration);
    NODE_SET_METHOD(exports, "getRecordedSamples", GetRecordedSamples);
}

NODE_MODULE(NODE_GYP_MODULE_NAME, Initialize)

} // namespace BladeRFAddon
