#ifndef PLATFORM_DETECT_H
#define PLATFORM_DETECT_H

#include <string>

namespace PlatformDetect {
    
    struct PlatformInfo {
        std::string architecture;
        std::string cpu_model;
        std::string os_name;
        std::string os_version;
        bool is_raspberry_pi;
        int raspberry_pi_version;
        bool has_neon;
        bool is_64bit;
    };
    
    // Platform detection functions
    PlatformInfo detectPlatform();
    std::string getArchitecture();
    std::string getCPUModel();
    bool isRaspberryPi();
    int getRaspberryPiVersion();
    bool hasNEON();
    bool is64Bit();
    
    // Performance optimization hints
    int getOptimalThreadCount();
    bool shouldUseNEON();
    std::string getOptimizationFlags();
    
} // namespace PlatformDetect

#endif // PLATFORM_DETECT_H
