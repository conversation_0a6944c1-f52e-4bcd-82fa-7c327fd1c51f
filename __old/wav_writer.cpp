#include "wav_writer.h"
#include <cstring>
#include <iostream>

namespace WavWriter {
    
    IQWavWriter::IQWavWriter() : sample_rate(0), samples_written(0), is_open(false) {
    }
    
    IQWavWriter::~IQWavWriter() {
        if (is_open) {
            close();
        }
    }
    
    bool IQWavWriter::open(const std::string& filename, uint32_t sample_rate) {
        if (is_open) {
            close();
        }
        
        this->filename = filename;
        this->sample_rate = sample_rate;
        this->samples_written = 0;
        
        file.open(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "Failed to open WAV file: " << filename << std::endl;
            return false;
        }
        
        writeHeader();
        is_open = true;
        return true;
    }
    
    void IQWavWriter::writeHeader() {
        WavHeader header;
        
        // RIFF Header
        std::memcpy(header.riff_header, "RIFF", 4);
        header.wav_size = 36; // Will be updated when closing
        std::memcpy(header.wave_header, "WAVE", 4);
        
        // Format Header
        std::memcpy(header.fmt_header, "fmt ", 4);
        header.fmt_chunk_size = 16;
        header.audio_format = 1; // PCM
        header.num_channels = 2; // I and Q channels
        header.sample_rate = sample_rate;
        header.bit_depth = 16;
        header.sample_alignment = header.num_channels * header.bit_depth / 8;
        header.byte_rate = header.sample_rate * header.sample_alignment;
        
        // Data Header
        std::memcpy(header.data_header, "data", 4);
        header.data_bytes = 0; // Will be updated when closing
        
        file.write(reinterpret_cast<const char*>(&header), sizeof(header));
    }
    
    bool IQWavWriter::writeIQSamples(const int16_t* i_samples, const int16_t* q_samples, size_t num_samples) {
        if (!is_open) {
            return false;
        }
        
        // Interleave I and Q samples
        std::vector<int16_t> interleaved(num_samples * 2);
        for (size_t i = 0; i < num_samples; i++) {
            interleaved[i * 2] = i_samples[i];     // I channel (left)
            interleaved[i * 2 + 1] = q_samples[i]; // Q channel (right)
        }
        
        file.write(reinterpret_cast<const char*>(interleaved.data()), 
                   interleaved.size() * sizeof(int16_t));
        
        if (!file.good()) {
            std::cerr << "Error writing to WAV file" << std::endl;
            return false;
        }
        
        samples_written += num_samples;
        return true;
    }
    
    bool IQWavWriter::writeInterleavedIQ(const int16_t* iq_samples, size_t num_samples) {
        if (!is_open) {
            return false;
        }
        
        // iq_samples should already be interleaved (I, Q, I, Q, ...)
        file.write(reinterpret_cast<const char*>(iq_samples), 
                   num_samples * 2 * sizeof(int16_t));
        
        if (!file.good()) {
            std::cerr << "Error writing to WAV file" << std::endl;
            return false;
        }
        
        samples_written += num_samples;
        return true;
    }
    
    void IQWavWriter::close() {
        if (!is_open) {
            return;
        }
        
        updateHeader();
        file.close();
        is_open = false;
    }
    
    void IQWavWriter::updateHeader() {
        if (!file.is_open()) {
            return;
        }
        
        // Calculate final sizes
        uint32_t data_bytes = samples_written * 2 * sizeof(int16_t); // 2 channels, 16-bit
        uint32_t wav_size = 36 + data_bytes;
        
        // Update wav_size in RIFF header
        file.seekp(4);
        file.write(reinterpret_cast<const char*>(&wav_size), sizeof(wav_size));
        
        // Update data_bytes in data header
        file.seekp(40);
        file.write(reinterpret_cast<const char*>(&data_bytes), sizeof(data_bytes));
        
        file.flush();
    }
    
} // namespace WavWriter
