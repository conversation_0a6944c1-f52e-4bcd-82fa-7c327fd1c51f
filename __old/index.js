const addon = require('../build/Release/bladerf_addon');

class BladeRF {
    constructor() {
        this.isOpen = false;
    }

    // Hello World function
    helloWorld() {
        return addon.helloWorld();
    }

    // Get library version
    getLibraryVersion() {
        return addon.getLibraryVersion();
    }

    // Get list of available devices
    getDeviceList() {
        return addon.getDeviceList();
    }

    // Open a device
    openDevice(deviceId = '') {
        const success = addon.openDevice(deviceId);
        if (success) {
            this.isOpen = true;
        }
        return success;
    }

    // Close the device
    closeDevice() {
        const success = addon.closeDevice();
        if (success) {
            this.isOpen = false;
        }
        return success;
    }

    // Check if device is open
    isDeviceOpen() {
        return addon.isDeviceOpen();
    }

    // Set frequency in Hz
    setFrequency(frequencyHz) {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.setFrequency(frequencyHz);
    }

    // Set sample rate in Hz
    setSampleRate(sampleRateHz) {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.setSampleRate(sampleRateHz);
    }

    // Set bandwidth in Hz
    setBandwidth(bandwidthHz) {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.setBandwidth(bandwidthHz);
    }

    // Set gain in dB
    setGain(gainDb) {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.setGain(gainDb);
    }

    // Get current configuration
    getCurrentConfig() {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.getCurrentConfig();
    }

    // Get device serial number
    getDeviceSerial() {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.getDeviceSerial();
    }

    // Get firmware version
    getFirmwareVersion() {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.getFirmwareVersion();
    }

    // Get FPGA version
    getFPGAVersion() {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.getFPGAVersion();
    }

    // Perform basic test
    performBasicTest() {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }
        return addon.performBasicTest();
    }

    // Convenience method to configure device with common settings
    configure(config = {}) {
        const defaultConfig = {
            frequency: 915000000,    // 915 MHz
            sampleRate: 1000000,     // 1 MHz
            bandwidth: 1500000,      // 1.5 MHz
            gain: 30                 // 30 dB
        };

        const finalConfig = { ...defaultConfig, ...config };
        
        let success = true;
        
        if (finalConfig.frequency) {
            success &= this.setFrequency(finalConfig.frequency);
        }
        
        if (finalConfig.sampleRate) {
            success &= this.setSampleRate(finalConfig.sampleRate);
        }
        
        if (finalConfig.bandwidth) {
            success &= this.setBandwidth(finalConfig.bandwidth);
        }
        
        if (finalConfig.gain !== undefined) {
            success &= this.setGain(finalConfig.gain);
        }
        
        return success;
    }

    // Get device information
    getDeviceInfo() {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }

        return {
            serial: this.getDeviceSerial(),
            firmwareVersion: this.getFirmwareVersion(),
            fpgaVersion: this.getFPGAVersion(),
            config: this.getCurrentConfig()
        };
    }

    // Get platform information
    getPlatformInfo() {
        return addon.getPlatformInfo();
    }

    // Get optimal thread count for this platform
    getOptimalThreadCount() {
        return addon.getOptimalThreadCount();
    }

    // Start IQ recording to WAV file
    startIQRecording(filename, maxDurationSeconds = 60) {
        if (!this.isDeviceOpen()) {
            throw new Error('Device is not open');
        }

        if (typeof filename !== 'string' || filename.length === 0) {
            throw new Error('Filename must be a non-empty string');
        }

        if (typeof maxDurationSeconds !== 'number' || maxDurationSeconds <= 0) {
            throw new Error('Max duration must be a positive number');
        }

        return addon.startIQRecording(filename, maxDurationSeconds);
    }

    // Stop IQ recording
    stopIQRecording() {
        return addon.stopIQRecording();
    }

    // Check if currently recording
    isRecording() {
        return addon.isRecording();
    }

    // Get current recording duration in seconds
    getRecordingDuration() {
        return addon.getRecordingDuration();
    }

    // Get number of recorded samples
    getRecordedSamples() {
        return addon.getRecordedSamples();
    }

    // Convenience method to record for a specific duration
    async recordIQ(filename, durationSeconds = 10) {
        return new Promise((resolve, reject) => {
            if (!this.startIQRecording(filename, durationSeconds)) {
                reject(new Error('Failed to start IQ recording'));
                return;
            }

            // Poll recording status
            const checkInterval = setInterval(() => {
                if (!this.isRecording()) {
                    clearInterval(checkInterval);
                    const duration = this.getRecordingDuration();
                    const samples = this.getRecordedSamples();
                    resolve({
                        filename: filename,
                        duration: duration,
                        samples: samples,
                        sampleRate: this.getCurrentConfig().sampleRate
                    });
                }
            }, 100);

            // Safety timeout (duration + 5 seconds)
            setTimeout(() => {
                if (this.isRecording()) {
                    clearInterval(checkInterval);
                    this.stopIQRecording();
                    const duration = this.getRecordingDuration();
                    const samples = this.getRecordedSamples();
                    resolve({
                        filename: filename,
                        duration: duration,
                        samples: samples,
                        sampleRate: this.getCurrentConfig().sampleRate
                    });
                }
            }, (durationSeconds + 5) * 1000);
        });
    }
}

module.exports = BladeRF;
