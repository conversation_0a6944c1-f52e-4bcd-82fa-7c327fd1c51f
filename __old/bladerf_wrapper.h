#ifndef BLADERF_WRAPPER_H
#define BLADERF_WRAPPER_H

#include <string>
#include <vector>

namespace BladeRFWrapper {
    
    struct DeviceInfo {
        std::string serial;
        std::string backend;
        std::string instance;
        bool available;
    };
    
    struct DeviceConfig {
        uint32_t frequency;
        uint32_t sample_rate;
        uint32_t bandwidth;
        int gain;
    };
    
    // Core functions
    std::string getLibraryVersion();
    std::vector<DeviceInfo> getDeviceList();
    bool openDevice(const std::string& device_identifier = "");
    bool closeDevice();
    bool isDeviceOpen();
    
    // Configuration functions
    bool setFrequency(uint64_t frequency_hz);
    bool setSampleRate(uint32_t sample_rate);
    bool setBandwidth(uint32_t bandwidth_hz);
    bool setGain(int gain_db);
    
    // Status functions
    DeviceConfig getCurrentConfig();
    std::string getDeviceSerial();
    std::string getFirmwareVersion();
    std::string getFPGAVersion();
    
    // Simple test function
    bool performBasicTest();

    // IQ Recording functions
    bool startIQRecording(const std::string& filename, double max_duration_seconds);
    bool stopIQRecording();
    bool isRecording();
    double getRecordingDuration();
    uint32_t getRecordedSamples();

} // namespace BladeRFWrapper

#endif // BLADERF_WRAPPER_H
