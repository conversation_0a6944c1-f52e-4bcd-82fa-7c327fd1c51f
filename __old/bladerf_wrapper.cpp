#include "bladerf_wrapper.h"
#include "wav_writer.h"
#include <libbladeRF.h>
#include <iostream>
#include <sstream>
#include <cstring>
#include <thread>
#include <atomic>
#include <chrono>
#include <memory>

namespace BladeRFWrapper {

    static struct bladerf* device = nullptr;

    // IQ Recording state
    static std::atomic<bool> recording_active{false};
    static std::unique_ptr<std::thread> recording_thread;
    static std::unique_ptr<WavWriter::IQWavWriter> wav_writer;
    static std::atomic<double> max_recording_duration{0.0};
    static std::chrono::steady_clock::time_point recording_start_time;
    
    std::string getLibraryVersion() {
        struct bladerf_version version;
        bladerf_version(&version);
        
        std::stringstream ss;
        ss << version.major << "." << version.minor << "." << version.patch;
        if (version.describe && strlen(version.describe) > 0) {
            ss << " (" << version.describe << ")";
        }
        return ss.str();
    }
    
    std::vector<DeviceInfo> getDeviceList() {
        std::vector<DeviceInfo> devices;
        struct bladerf_devinfo* dev_list;
        int num_devices = bladerf_get_device_list(&dev_list);
        
        if (num_devices < 0) {
            return devices; // Return empty vector on error
        }
        
        for (int i = 0; i < num_devices; i++) {
            DeviceInfo info;
            info.serial = std::string(dev_list[i].serial);
            info.backend = bladerf_backend_str(dev_list[i].backend);
            info.instance = std::to_string(dev_list[i].instance);
            info.available = true;
            devices.push_back(info);
        }
        
        bladerf_free_device_list(dev_list);
        return devices;
    }
    
    bool openDevice(const std::string& device_identifier) {
        if (device != nullptr) {
            return true; // Already open
        }
        
        const char* dev_id = device_identifier.empty() ? nullptr : device_identifier.c_str();
        int status = bladerf_open(&device, dev_id);
        
        if (status != 0) {
            std::cerr << "Failed to open bladeRF device: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        return true;
    }
    
    bool closeDevice() {
        if (device == nullptr) {
            return true; // Already closed
        }
        
        bladerf_close(device);
        device = nullptr;
        return true;
    }
    
    bool isDeviceOpen() {
        return device != nullptr;
    }
    
    bool setFrequency(uint64_t frequency_hz) {
        if (device == nullptr) {
            return false;
        }
        
        int status = bladerf_set_frequency(device, BLADERF_CHANNEL_RX(0), frequency_hz);
        if (status != 0) {
            std::cerr << "Failed to set RX frequency: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        status = bladerf_set_frequency(device, BLADERF_CHANNEL_TX(0), frequency_hz);
        if (status != 0) {
            std::cerr << "Failed to set TX frequency: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        return true;
    }
    
    bool setSampleRate(uint32_t sample_rate) {
        if (device == nullptr) {
            return false;
        }
        
        uint32_t actual_rate;
        int status = bladerf_set_sample_rate(device, BLADERF_CHANNEL_RX(0), sample_rate, &actual_rate);
        if (status != 0) {
            std::cerr << "Failed to set RX sample rate: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        status = bladerf_set_sample_rate(device, BLADERF_CHANNEL_TX(0), sample_rate, &actual_rate);
        if (status != 0) {
            std::cerr << "Failed to set TX sample rate: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        return true;
    }
    
    bool setBandwidth(uint32_t bandwidth_hz) {
        if (device == nullptr) {
            return false;
        }
        
        uint32_t actual_bandwidth;
        int status = bladerf_set_bandwidth(device, BLADERF_CHANNEL_RX(0), bandwidth_hz, &actual_bandwidth);
        if (status != 0) {
            std::cerr << "Failed to set RX bandwidth: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        status = bladerf_set_bandwidth(device, BLADERF_CHANNEL_TX(0), bandwidth_hz, &actual_bandwidth);
        if (status != 0) {
            std::cerr << "Failed to set TX bandwidth: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        return true;
    }
    
    bool setGain(int gain_db) {
        if (device == nullptr) {
            return false;
        }
        
        int status = bladerf_set_gain(device, BLADERF_CHANNEL_RX(0), gain_db);
        if (status != 0) {
            std::cerr << "Failed to set RX gain: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        status = bladerf_set_gain(device, BLADERF_CHANNEL_TX(0), gain_db);
        if (status != 0) {
            std::cerr << "Failed to set TX gain: " << bladerf_strerror(status) << std::endl;
            return false;
        }
        
        return true;
    }
    
    DeviceConfig getCurrentConfig() {
        DeviceConfig config = {0, 0, 0, 0};
        
        if (device == nullptr) {
            return config;
        }
        
        bladerf_frequency freq;
        bladerf_sample_rate rate;
        bladerf_bandwidth bw;
        bladerf_gain gain;
        
        if (bladerf_get_frequency(device, BLADERF_CHANNEL_RX(0), &freq) == 0) {
            config.frequency = freq;
        }
        
        if (bladerf_get_sample_rate(device, BLADERF_CHANNEL_RX(0), &rate) == 0) {
            config.sample_rate = rate;
        }
        
        if (bladerf_get_bandwidth(device, BLADERF_CHANNEL_RX(0), &bw) == 0) {
            config.bandwidth = bw;
        }
        
        if (bladerf_get_gain(device, BLADERF_CHANNEL_RX(0), &gain) == 0) {
            config.gain = gain;
        }
        
        return config;
    }
    
    std::string getDeviceSerial() {
        if (device == nullptr) {
            return "";
        }
        
        struct bladerf_devinfo info;
        int status = bladerf_get_devinfo(device, &info);
        if (status != 0) {
            return "";
        }
        
        return std::string(info.serial);
    }
    
    std::string getFirmwareVersion() {
        if (device == nullptr) {
            return "";
        }
        
        struct bladerf_version version;
        int status = bladerf_fw_version(device, &version);
        if (status != 0) {
            return "";
        }
        
        std::stringstream ss;
        ss << version.major << "." << version.minor << "." << version.patch;
        return ss.str();
    }
    
    std::string getFPGAVersion() {
        if (device == nullptr) {
            return "";
        }
        
        struct bladerf_version version;
        int status = bladerf_fpga_version(device, &version);
        if (status != 0) {
            return "";
        }
        
        std::stringstream ss;
        ss << version.major << "." << version.minor << "." << version.patch;
        return ss.str();
    }
    
    bool performBasicTest() {
        if (device == nullptr) {
            return false;
        }

        // Perform basic configuration test
        bool success = true;

        // Test setting frequency to 915 MHz
        success &= setFrequency(915000000);

        // Test setting sample rate to 1 MHz
        success &= setSampleRate(1000000);

        // Test setting bandwidth to 1.5 MHz
        success &= setBandwidth(1500000);

        // Test setting gain to 30 dB
        success &= setGain(30);

        return success;
    }

    // IQ Recording thread function
    static void recordingThreadFunction() {
        if (!device || !wav_writer) {
            recording_active = false;
            return;
        }

        const size_t buffer_size = 8192; // Number of IQ samples per buffer
        const size_t timeout_ms = 3000;

        // Allocate buffers for IQ samples
        std::vector<int16_t> rx_buffer(buffer_size * 2); // Interleaved I/Q

        // Configure sync interface
        int status = bladerf_sync_config(device, BLADERF_RX_X1, BLADERF_FORMAT_SC16_Q11,
                                        64, buffer_size, 8, timeout_ms);
        if (status != 0) {
            std::cerr << "Failed to configure RX sync: " << bladerf_strerror(status) << std::endl;
            recording_active = false;
            return;
        }

        // Enable RX module
        status = bladerf_enable_module(device, BLADERF_RX, true);
        if (status != 0) {
            std::cerr << "Failed to enable RX: " << bladerf_strerror(status) << std::endl;
            recording_active = false;
            return;
        }

        std::cout << "IQ recording started..." << std::endl;

        while (recording_active) {
            // Check if we've exceeded the maximum duration
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - recording_start_time).count() / 1000.0;

            if (max_recording_duration > 0 && elapsed >= max_recording_duration) {
                std::cout << "Maximum recording duration reached: " << elapsed << " seconds" << std::endl;
                break;
            }

            // Receive IQ samples
            status = bladerf_sync_rx(device, rx_buffer.data(), buffer_size, nullptr, timeout_ms);
            if (status != 0) {
                if (status == BLADERF_ERR_TIMEOUT) {
                    std::cout << "RX timeout, continuing..." << std::endl;
                    continue;
                } else {
                    std::cerr << "RX failed: " << bladerf_strerror(status) << std::endl;
                    break;
                }
            }

            // Write samples to WAV file
            if (!wav_writer->writeInterleavedIQ(rx_buffer.data(), buffer_size)) {
                std::cerr << "Failed to write samples to WAV file" << std::endl;
                break;
            }
        }

        // Disable RX module
        bladerf_enable_module(device, BLADERF_RX, false);

        recording_active = false;
        std::cout << "IQ recording stopped. Duration: " << wav_writer->getDurationSeconds()
                  << " seconds, Samples: " << wav_writer->getSamplesWritten() << std::endl;
    }

    bool startIQRecording(const std::string& filename, double max_duration_seconds) {
        if (device == nullptr) {
            std::cerr << "Device is not open" << std::endl;
            return false;
        }

        if (recording_active) {
            std::cerr << "Recording is already active" << std::endl;
            return false;
        }

        // Get current sample rate
        bladerf_sample_rate sample_rate;
        int status = bladerf_get_sample_rate(device, BLADERF_RX, &sample_rate);
        if (status != 0) {
            std::cerr << "Failed to get sample rate: " << bladerf_strerror(status) << std::endl;
            return false;
        }

        // Create WAV writer
        wav_writer = std::make_unique<WavWriter::IQWavWriter>();
        if (!wav_writer->open(filename, sample_rate)) {
            std::cerr << "Failed to open WAV file: " << filename << std::endl;
            wav_writer.reset();
            return false;
        }

        // Set recording parameters
        max_recording_duration = max_duration_seconds;
        recording_start_time = std::chrono::steady_clock::now();
        recording_active = true;

        // Start recording thread
        recording_thread = std::make_unique<std::thread>(recordingThreadFunction);

        return true;
    }

    bool stopIQRecording() {
        if (!recording_active) {
            return false;
        }

        recording_active = false;

        if (recording_thread && recording_thread->joinable()) {
            recording_thread->join();
        }

        if (wav_writer) {
            wav_writer->close();
            wav_writer.reset();
        }

        recording_thread.reset();

        return true;
    }

    bool isRecording() {
        return recording_active;
    }

    double getRecordingDuration() {
        if (!recording_active || !wav_writer) {
            return 0.0;
        }
        return wav_writer->getDurationSeconds();
    }

    uint32_t getRecordedSamples() {
        if (!wav_writer) {
            return 0;
        }
        return wav_writer->getSamplesWritten();
    }

} // namespace BladeRFWrapper
