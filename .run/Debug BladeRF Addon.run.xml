<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Debug BladeRF Addon" type="CMakeRunConfiguration" factoryName="Application" PROGRAM_PARAMS="$ProjectFileDir$/js/debug-bladerf-addon.js" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" WORKING_DIR="file://$ProjectFileDir$" PASS_PARENT_ENVS_2="true" PROJECT_NAME="bladerf_addon" TARGET_NAME="bladerf_addon" CONFIG_NAME="Debug" RUN_PATH="$USER_HOME$/.nvm/versions/node/v22.16.0/bin/node">
    <method v="2">
      <option name="RunConfigurationTask" enabled="true" run_configuration_name="build" run_configuration_type="js.build_tools.npm" />
    </method>
  </configuration>
</component>