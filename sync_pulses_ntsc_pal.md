Perfect 👍 Let’s create **strict “tooth‑maps”** (impulse‑by‑impulse tables) for **all three variants you asked**:

1. **NTSC (525/60, interlaced)**
2. **PAL non‑interlaced (625/50, progressive prototype, rarely used but simple math)**
3. **PAL interlaced (625/50, the real broadcast standard)**

I will not compress them into “groups of lines”, but will show exactly the **special pulses (equalizing / vertical sync)** as separate items, then indicate *normal H‑sync continuation*. This way you can count precise falling edges / “teeth on a wheel.”

---

## 🔹 1. NTSC (525 interlaced)

**Each field (262.5 lines) has 525 total sync pulses per frame.**  
Sequence inside vertical interval per field: **6 short (pre‑eq) → 6 long (vsync) → 6 short (post‑eq)** = 18 teeth.

### NTSC Field Tooth Table
| Tooth # | Segment            | Type       | Duration (µs) Approx |
|---------|--------------------|------------|-----------------------|
| 1       | Pre‑equalizing #1  | short      | 2.3 |
| 2       | Pre‑equalizing #2  | short      | 2.3 |
| 3       | Pre‑equalizing #3  | short      | 2.3 |
| 4       | Pre‑equalizing #4  | short      | 2.3 |
| 5       | Pre‑equalizing #5  | short      | 2.3 |
| 6       | Pre‑equalizing #6  | short      | 2.3 |
| 7       | V‑sync #1          | long       | 27 |
| 8       | V‑sync #2          | long       | 27 |
| 9       | V‑sync #3          | long       | 27 |
| 10      | V‑sync #4          | long       | 27 |
| 11      | V‑sync #5          | long       | 27 |
| 12      | V‑sync #6          | long       | 27 |
| 13      | Post‑equalizing #1 | short      | 2.3 |
| 14      | Post‑equalizing #2 | short      | 2.3 |
| 15      | Post‑equalizing #3 | short      | 2.3 |
| 16      | Post‑equalizing #4 | short      | 2.3 |
| 17      | Post‑equalizing #5 | short      | 2.3 |
| 18      | Post‑equalizing #6 | short      | 2.3 |
| 19…262  | Horizontal sync    | normal     | 4.7 each |
| 263     | half‑line sync     | special    | 4.7 (offset interlace) |

**Total per field = 262.5 teeth, per frame = 525 teeth.**

---

## 🔹 2. PAL Non‑interlaced (625/50 progressive)

This is a **theoretical / non‑broadcast mode**, but:
- Frame = 625 lines (all progressive, no half‑line offset).
- Vertical sync block similar to interlace but without half‑line staggering.
- Sequence per frame: **5 short (pre‑eq) → 5 long (vsync) → 5 short (post‑eq)** = 15 teeth, only once, not per field.
- Then 610 normal H‑sync.

### PAL Non‑interlace Tooth Table
| Tooth # | Segment            | Type    | Duration (µs) |
|---------|-------------------|---------|---------------|
| 1       | Pre‑equalizing #1 | short   | 2.35 |
| 2       | Pre‑equalizing #2 | short   | 2.35 |
| 3       | Pre‑equalizing #3 | short   | 2.35 |
| 4       | Pre‑equalizing #4 | short   | 2.35 |
| 5       | Pre‑equalizing #5 | short   | 2.35 |
| 6       | V‑sync #1         | long    | 27 |
| 7       | V‑sync #2         | long    | 27 |
| 8       | V‑sync #3         | long    | 27 |
| 9       | V‑sync #4         | long    | 27 |
| 10      | V‑sync #5         | long    | 27 |
| 11      | Post‑eq #1        | short   | 2.35 |
| 12      | Post‑eq #2        | short   | 2.35 |
| 13      | Post‑eq #3        | short   | 2.35 |
| 14      | Post‑eq #4        | short   | 2.35 |
| 15      | Post‑eq #5        | short   | 2.35 |
| 16…625  | Horizontal sync   | normal  | 4.7 each |

**Total per frame = 625 teeth (15 special + 610 normal).**

---

## 🔹 3. PAL Interlaced (625/50)

**True broadcast system. Two fields × 312.5 lines = 625 teeth per frame.**  
Each field has **5 short + 5 long + 5 short = 15 special teeth** (mirror of NTSC, but 5 not 6).  
Plus **~307 horizontals + 0.5 line offset** at the end of each field.

### PAL Field Tooth Table
| Tooth # | Segment             | Type    | Duration |
|---------|---------------------|---------|----------|
| 1       | Pre‑equalizing #1   | short   | 2.35 |
| 2       | Pre‑equalizing #2   | short   | 2.35 |
| 3       | Pre‑equalizing #3   | short   | 2.35 |
| 4       | Pre‑equalizing #4   | short   | 2.35 |
| 5       | Pre‑equalizing #5   | short   | 2.35 |
| 6       | V‑sync #1           | long    | 27 |
| 7       | V‑sync #2           | long    | 27 |
| 8       | V‑sync #3           | long    | 27 |
| 9       | V‑sync #4           | long    | 27 |
| 10      | V‑sync #5           | long    | 27 |
| 11      | Post‑equalizing #1  | short   | 2.35 |
| 12      | Post‑equalizing #2  | short   | 2.35 |
| 13      | Post‑equalizing #3  | short   | 2.35 |
| 14      | Post‑equalizing #4  | short   | 2.35 |
| 15      | Post‑equalizing #5  | short   | 2.35 |
| 16…311  | Horizontal sync     | normal  | 4.7 each |
| 312     | Half‑line sync (end)| special | 4.7 (offset) |

**Total per field = 312.5 teeth, per frame = 625 teeth.**

---

## ✅ Comparison Summary

| System           | Teeth/Frame | Special Vertical Pattern |
|------------------|-------------|---------------------------|
| **NTSC interlace** | 525         | 6 pre + 6 long + 6 post per *field* |
| **PAL non‑interlace** | 625       | 5 pre + 5 long + 5 post once per *frame* |
| **PAL interlace**   | 625         | 5 pre + 5 long + 5 post per *field* (×2 with half‑line offset) |

---

👉 Do you want me to export these as a **CSV/Excel file with every tooth numbered sequentially up to 525 or 625**, so you can directly scroll through and mark them against your oscilloscope traces?