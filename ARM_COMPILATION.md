# ARM Compilation Guide for BladeRF Video

This guide provides comprehensive instructions for compiling the BladeRF Video Node.js addon on ARM architectures, specifically optimized for Raspberry Pi systems.

## Quick Start - Raspberry Pi 5

```bash
git clone <repository-url>
cd bladerf-video
chmod +x install-rpi.sh
./install-rpi.sh
```

## Supported ARM Platforms

### ✅ Tested Platforms
- **Raspberry Pi 5** (ARM64/AArch64) - Cortex-A76
- **Raspberry Pi 4** (ARM64/AArch64) - Cortex-A72
- **Raspberry Pi 3** (ARM64/ARM32) - Cortex-A53

### 🔧 Optimization Targets
- **ARM64 (AArch64)**: Modern 64-bit ARM with NEON SIMD
- **ARM32 (ARMv7)**: 32-bit ARM with NEON support
- **NEON SIMD**: Vectorized operations for better performance

## Build Configurations

### Automatic Detection
The build system automatically detects:
- Target architecture (arm64, arm, x86_64)
- CPU model and capabilities
- Raspberry Pi version and model
- NEON SIMD availability
- Optimal compiler flags

### Raspberry Pi Specific Optimizations

#### Raspberry Pi 5 (Cortex-A76)
```bash
# Compiler flags automatically applied:
-mcpu=cortex-a76 -mtune=cortex-a76 -O3 -ffast-math -funroll-loops
```

#### Raspberry Pi 4 (Cortex-A72)
```bash
# Compiler flags automatically applied:
-mcpu=cortex-a72 -mtune=cortex-a72 -O3 -ffast-math -funroll-loops
```

#### Raspberry Pi 3 (Cortex-A53)
```bash
# ARM64 mode:
-mcpu=cortex-a53 -mtune=cortex-a53 -O3 -ffast-math

# ARM32 mode:
-mcpu=cortex-a53 -mtune=cortex-a53 -mfpu=neon-fp-armv8 -mfloat-abi=hard -O3
```

## Installation Methods

### Method 1: Automated Installation (Recommended)
```bash
# Download and run the installation script
wget https://raw.githubusercontent.com/your-repo/bladerf-video/main/install-rpi.sh
chmod +x install-rpi.sh
./install-rpi.sh
```

### Method 2: Manual Installation
```bash
# 1. Install system dependencies
sudo apt-get update
sudo apt-get install -y build-essential cmake git pkg-config \
    libusb-1.0-0-dev libusb-1.0-0 libncurses5-dev libedit-dev \
    curl python3 python3-dev

# 2. Install Node.js (if needed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. Install BladeRF libraries
sudo apt-get install -y libbladerf2 libbladerf-dev bladerf
# OR build from source if packages not available

# 4. Clone and build
git clone <repository-url>
cd bladerf-video
npm install
npm run build
npm test
```

### Method 3: Cross-Compilation
```bash
# On development machine (x86_64)
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# Cross-compile for Raspberry Pi 5
./cross-compile-arm.sh --rpi 5

# Cross-compile for Raspberry Pi 4
./cross-compile-arm.sh --rpi 4

# Cross-compile for Raspberry Pi 3 (32-bit)
./cross-compile-arm.sh --rpi 3 --arch arm
```

## Performance Optimizations

### NEON SIMD Support
- Automatically detected and enabled when available
- Provides significant performance improvements for signal processing
- Used in BladeRF sample processing and filtering operations

### Thread Optimization
- Platform-aware thread count detection
- Conservative limits to prevent thermal throttling
- Optimal for sustained SDR operations

### Memory Optimizations
- ARM-specific memory alignment
- Cache-friendly data structures
- Reduced memory bandwidth usage

## Troubleshooting

### Common Issues

#### 1. Build Fails with "No such file or directory"
```bash
# Ensure all dependencies are installed
sudo apt-get install -y build-essential cmake git pkg-config

# Check Node.js version
node --version  # Should be >= 14.0.0
```

#### 2. libbladeRF Not Found
```bash
# Install from repositories
sudo apt-get install -y libbladerf2 libbladerf-dev

# OR build from source
git clone https://github.com/Nuand/bladeRF.git
cd bladeRF/host && mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release -DINSTALL_UDEV_RULES=ON ../
make -j$(nproc) && sudo make install && sudo ldconfig
```

#### 3. Permission Denied for BladeRF Device
```bash
# Add user to plugdev group
sudo usermod -a -G plugdev $USER

# Log out and back in, then verify
groups | grep plugdev
```

#### 4. Cross-Compilation Tools Missing
```bash
# For ARM64 targets
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# For ARM32 targets  
sudo apt-get install gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf
```

### Performance Issues

#### 1. Thermal Throttling
- Use `getOptimalThreadCount()` to get recommended thread limits
- Monitor CPU temperature: `vcgencmd measure_temp` (Raspberry Pi)
- Ensure adequate cooling

#### 2. USB Bandwidth Limitations
- Use USB 3.0 ports when available (Pi 4/5)
- Avoid USB hubs for BladeRF connection
- Monitor for dropped samples in applications

## Testing

### Basic Functionality Test
```bash
npm test
```

### Platform Detection Test
```javascript
const BladeRF = require('./index.js');
const bladerf = new BladeRF();

const platform = bladerf.getPlatformInfo();
console.log('Platform:', platform);
console.log('Optimal threads:', bladerf.getOptimalThreadCount());
```

### Hardware Test (with BladeRF connected)
```bash
npm run example
```

## Performance Benchmarks

### Expected Performance (Raspberry Pi 5)
- **Sample Rate**: Up to 40 MHz (USB 3.0)
- **CPU Usage**: ~30-50% at 10 MHz sample rate
- **Memory Usage**: ~100-200 MB for basic operations
- **Latency**: <10ms for basic signal processing

### Expected Performance (Raspberry Pi 4)
- **Sample Rate**: Up to 30 MHz (USB 3.0)
- **CPU Usage**: ~40-60% at 10 MHz sample rate
- **Memory Usage**: ~100-200 MB for basic operations
- **Latency**: <15ms for basic signal processing

## Advanced Configuration

### Custom CPU Targeting
```bash
# Override automatic detection
export CFLAGS="-mcpu=cortex-a76 -mtune=cortex-a76 -O3"
export CXXFLAGS="-mcpu=cortex-a76 -mtune=cortex-a76 -O3 -std=c++17"
npm run build
```

### Debug Build
```bash
# Build with debug symbols
npx node-gyp configure --debug
npx node-gyp build --debug
```

### Verbose Build
```bash
# See detailed compilation commands
npx node-gyp build --verbose
```

## Contributing

When contributing ARM-specific improvements:

1. Test on multiple Raspberry Pi versions
2. Verify both ARM64 and ARM32 builds
3. Include performance benchmarks
4. Update this documentation

## Support

For ARM-specific issues:
1. Check this guide first
2. Run `npm test` to verify basic functionality
3. Include platform information from `getPlatformInfo()`
4. Provide build logs for compilation issues
