#include "../wav_stream.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <cstring>
#include <memory>
#include <cmath>

// Timing test utilities
class TimingTestUtils {
public:
    // Create a test WAV file for timing tests
    static bool createTimingTestWAV(const std::string& filename, 
                                   uint32_t sampleRate = 44100,
                                   uint32_t numSamples = 1000) {
        
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        // WAV header
        struct WAVHeader {
            char riff[4] = {'R', 'I', 'F', 'F'};
            uint32_t fileSize;
            char wave[4] = {'W', 'A', 'V', 'E'};
            char fmt[4] = {'f', 'm', 't', ' '};
            uint32_t fmtSize = 16;
            uint16_t audioFormat = 1; // PCM
            uint16_t numChannels = 2; // Stereo (I/Q)
            uint32_t sampleRate;
            uint32_t byteRate;
            uint16_t blockAlign = 4; // 2 channels * 2 bytes
            uint16_t bitsPerSample = 16;
            char data[4] = {'d', 'a', 't', 'a'};
            uint32_t dataSize;
        } header;
        
        header.sampleRate = sampleRate;
        header.byteRate = sampleRate * 2 * 2; // sampleRate * channels * bytesPerSample
        header.dataSize = numSamples * 4; // numSamples * channels * bytesPerSample
        header.fileSize = sizeof(header) - 8 + header.dataSize;
        
        file.write(reinterpret_cast<const char*>(&header), sizeof(header));
        
        // Generate test data (simple pattern)
        for (uint32_t i = 0; i < numSamples; ++i) {
            int16_t iSample = static_cast<int16_t>(i & 0xFFFF);
            int16_t qSample = static_cast<int16_t>((i + 1000) & 0xFFFF);
            
            file.write(reinterpret_cast<const char*>(&iSample), sizeof(iSample));
            file.write(reinterpret_cast<const char*>(&qSample), sizeof(qSample));
        }
        
        return file.good();
    }
    
    // Measure execution time in milliseconds
    static double measureTimeMs(std::function<void()> func) {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        return static_cast<double>(duration.count()) / 1000.0;
    }
    
    // Calculate expected duration for samples
    static double expectedDurationMs(size_t sampleCount, uint32_t sampleRate) {
        return (static_cast<double>(sampleCount) / static_cast<double>(sampleRate)) * 1000.0;
    }
    
    // Check if timing is within acceptable tolerance
    static bool isTimingAccurate(double actualMs, double expectedMs, double tolerancePercent = 30.0) {
        // For very short durations, use absolute tolerance
        if (expectedMs < 1.0) {
            return std::abs(actualMs - expectedMs) <= 1.0; // 1ms absolute tolerance for short durations
        }

        double tolerance = expectedMs * (tolerancePercent / 100.0);
        double diff = std::abs(actualMs - expectedMs);
        return diff <= tolerance;
    }
    
    // Cleanup test files
    static void cleanup(const std::vector<std::string>& files) {
        for (const auto& file : files) {
            std::remove(file.c_str());
        }
    }
};

// Test 1: Basic timing simulation functionality
int test_basic_timing_simulation() {
    std::cout << "\n--- Test 1: Basic Timing Simulation ---" << std::endl;
    
    const std::string testFile = "timing_test_basic.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test WAV file with 44.1kHz sample rate and enough samples
        if (!TimingTestUtils::createTimingTestWAV(testFile, 44100, 20000)) {
            std::cout << "❌ Failed to create timing test WAV file" << std::endl;
            return 1;
        }
        
        // Test with timing disabled (should be fast)
        WAVIQStream streamNoTiming(testFile, false, false);
        if (!streamNoTiming.open()) {
            std::cout << "❌ Failed to open WAV file: " << streamNoTiming.lastError() << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        std::vector<SampleType> buffer(100);
        double timeNoTiming = TimingTestUtils::measureTimeMs([&]() {
            streamNoTiming.readSamples(buffer.data(), 100);
        });
        
        std::cout << "✓ No timing: " << timeNoTiming << " ms" << std::endl;
        
        // Test with timing enabled using larger sample count for better accuracy
        WAVIQStream streamWithTiming(testFile, false, true);
        if (!streamWithTiming.open()) {
            std::cout << "❌ Failed to open WAV file with timing: " << streamWithTiming.lastError() << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }

        // Use larger sample count to get timing above the 100μs threshold
        const size_t largeSampleCount = 8000; // ~181ms at 44.1kHz
        std::vector<SampleType> largeBuffer(largeSampleCount);

        double timeWithTiming = TimingTestUtils::measureTimeMs([&]() {
            streamWithTiming.readSamples(largeBuffer.data(), largeSampleCount);
        });

        double expectedTime = TimingTestUtils::expectedDurationMs(largeSampleCount, 44100);

        std::cout << "✓ With timing: " << timeWithTiming << " ms" << std::endl;
        std::cout << "✓ Expected: " << expectedTime << " ms" << std::endl;

        // Verify timing is enabled
        if (!streamWithTiming.isTimingEnabled()) {
            std::cout << "❌ Timing should be enabled" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }

        // Verify timing accuracy (more lenient tolerance for system timing variations)
        if (!TimingTestUtils::isTimingAccurate(timeWithTiming, expectedTime, 30.0)) {
            std::cout << "❌ Timing not accurate enough. Expected: " << expectedTime
                     << " ms, Got: " << timeWithTiming << " ms" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        std::cout << "✓ Timing simulation is working correctly" << std::endl;
        
        TimingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in basic timing test: " << e.what() << std::endl;
        TimingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test 2: Timing accuracy across different sample rates
int test_timing_accuracy_sample_rates() {
    std::cout << "\n--- Test 2: Timing Accuracy Across Sample Rates ---" << std::endl;
    
    std::vector<uint32_t> sampleRates = {8000, 22050, 44100, 48000, 96000};
    std::vector<std::string> testFiles;
    
    try {
        for (size_t i = 0; i < sampleRates.size(); ++i) {
            uint32_t sampleRate = sampleRates[i];
            std::string testFile = "timing_test_" + std::to_string(sampleRate) + ".wav";
            testFiles.push_back(testFile);
            
            // Create test file with enough samples for meaningful timing
            size_t numSamples = std::max(static_cast<size_t>(sampleRate / 5), static_cast<size_t>(10000)); // At least 200ms or 10k samples
            if (!TimingTestUtils::createTimingTestWAV(testFile, sampleRate, numSamples)) {
                std::cout << "❌ Failed to create test WAV for " << sampleRate << " Hz" << std::endl;
                TimingTestUtils::cleanup(testFiles);
                return 1;
            }

            // Test timing accuracy
            WAVIQStream stream(testFile, false, true);
            if (!stream.open()) {
                std::cout << "❌ Failed to open WAV file for " << sampleRate << " Hz" << std::endl;
                TimingTestUtils::cleanup(testFiles);
                return 1;
            }

            // Use sample count that gives at least 100ms duration for accurate timing
            size_t sampleCount = std::max(static_cast<size_t>(sampleRate / 10), static_cast<size_t>(1000));
            std::vector<SampleType> buffer(sampleCount);

            double actualTime = TimingTestUtils::measureTimeMs([&]() {
                stream.readSamples(buffer.data(), sampleCount);
            });

            double expectedTime = TimingTestUtils::expectedDurationMs(sampleCount, sampleRate);

            std::cout << "✓ " << sampleRate << " Hz: Expected " << expectedTime
                     << " ms, Got " << actualTime << " ms";

            if (TimingTestUtils::isTimingAccurate(actualTime, expectedTime, 30.0)) {
                std::cout << " ✓" << std::endl;
            } else {
                std::cout << " ❌ (outside tolerance)" << std::endl;
                TimingTestUtils::cleanup(testFiles);
                return 1;
            }
        }
        
        std::cout << "✓ All sample rates show accurate timing" << std::endl;
        
        TimingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in sample rate timing test: " << e.what() << std::endl;
        TimingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test 3: Timing control methods
int test_timing_control_methods() {
    std::cout << "\n--- Test 3: Timing Control Methods ---" << std::endl;
    
    const std::string testFile = "timing_control_test.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test file with more samples for better timing accuracy
        if (!TimingTestUtils::createTimingTestWAV(testFile, 44100, 20000)) {
            std::cout << "❌ Failed to create timing control test WAV file" << std::endl;
            return 1;
        }
        
        // Test constructor with timing disabled
        WAVIQStream stream(testFile, false, false);
        if (!stream.open()) {
            std::cout << "❌ Failed to open WAV file" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Verify timing is initially disabled
        if (stream.isTimingEnabled()) {
            std::cout << "❌ Timing should be disabled initially" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Initial timing state correct (disabled)" << std::endl;
        
        // Enable timing
        stream.setTimingEnabled(true);
        if (!stream.isTimingEnabled()) {
            std::cout << "❌ Timing should be enabled after setTimingEnabled(true)" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Timing enabled successfully" << std::endl;
        
        // Test that timing actually works when enabled (use larger sample count)
        const size_t testSampleCount = 8000; // ~181ms at 44.1kHz
        std::vector<SampleType> buffer(testSampleCount);
        double timeWithTiming = TimingTestUtils::measureTimeMs([&]() {
            stream.readSamples(buffer.data(), testSampleCount);
        });

        double expectedTime = TimingTestUtils::expectedDurationMs(testSampleCount, 44100);

        if (!TimingTestUtils::isTimingAccurate(timeWithTiming, expectedTime, 30.0)) {
            std::cout << "❌ Timing not working after enabling. Expected: " << expectedTime
                     << " ms, Got: " << timeWithTiming << " ms" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Timing works correctly after enabling" << std::endl;
        
        // Disable timing
        stream.setTimingEnabled(false);
        if (stream.isTimingEnabled()) {
            std::cout << "❌ Timing should be disabled after setTimingEnabled(false)" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Timing disabled successfully" << std::endl;
        
        TimingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in timing control test: " << e.what() << std::endl;
        TimingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test 4: Timing with different chunk sizes
int test_timing_chunk_sizes() {
    std::cout << "\n--- Test 4: Timing with Different Chunk Sizes ---" << std::endl;

    const std::string testFile = "timing_chunk_test.wav";
    std::vector<std::string> testFiles = {testFile};
    std::vector<size_t> chunkSizes = {4410, 8820, 22050, 44100}; // 100ms, 200ms, 500ms, 1s at 44.1kHz

    try {
        // Create test file with enough samples for all chunk sizes
        if (!TimingTestUtils::createTimingTestWAV(testFile, 44100, 100000)) {
            std::cout << "❌ Failed to create chunk timing test WAV file" << std::endl;
            return 1;
        }

        for (size_t chunkSize : chunkSizes) {
            WAVIQStream stream(testFile, false, true);
            if (!stream.open()) {
                std::cout << "❌ Failed to open WAV file for chunk size " << chunkSize << std::endl;
                TimingTestUtils::cleanup(testFiles);
                return 1;
            }

            std::vector<SampleType> buffer(chunkSize);

            double actualTime = TimingTestUtils::measureTimeMs([&]() {
                stream.readSamples(buffer.data(), chunkSize);
            });

            double expectedTime = TimingTestUtils::expectedDurationMs(chunkSize, 44100);

            std::cout << "✓ Chunk size " << chunkSize << ": Expected " << expectedTime
                     << " ms, Got " << actualTime << " ms";

            if (TimingTestUtils::isTimingAccurate(actualTime, expectedTime, 30.0)) {
                std::cout << " ✓" << std::endl;
            } else {
                std::cout << " ❌ (outside tolerance)" << std::endl;
                TimingTestUtils::cleanup(testFiles);
                return 1;
            }
        }

        std::cout << "✓ All chunk sizes show accurate timing" << std::endl;

        TimingTestUtils::cleanup(testFiles);
        return 0;

    } catch (const std::exception& e) {
        std::cout << "❌ Exception in chunk size timing test: " << e.what() << std::endl;
        TimingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test 5: Performance impact assessment
int test_timing_performance_impact() {
    std::cout << "\n--- Test 5: Performance Impact Assessment ---" << std::endl;

    const std::string testFile = "timing_performance_test.wav";
    std::vector<std::string> testFiles = {testFile};

    try {
        // Create larger test file for performance testing
        if (!TimingTestUtils::createTimingTestWAV(testFile, 44100, 50000)) {
            std::cout << "❌ Failed to create performance test WAV file" << std::endl;
            return 1;
        }

        const size_t sampleCount = 1000;
        const int iterations = 10;

        // Measure performance without timing
        double totalTimeNoTiming = 0.0;
        for (int i = 0; i < iterations; ++i) {
            WAVIQStream stream(testFile, false, false);
            if (!stream.open()) {
                std::cout << "❌ Failed to open WAV file (no timing)" << std::endl;
                TimingTestUtils::cleanup(testFiles);
                return 1;
            }

            std::vector<SampleType> buffer(sampleCount);
            totalTimeNoTiming += TimingTestUtils::measureTimeMs([&]() {
                stream.readSamples(buffer.data(), sampleCount);
            });
        }

        double avgTimeNoTiming = totalTimeNoTiming / iterations;

        // Measure performance with timing (but use very high sample rate to minimize sleep)
        WAVIQStream streamHighRate(testFile, false, true);
        if (!streamHighRate.open()) {
            std::cout << "❌ Failed to open WAV file (with timing)" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }

        // Temporarily disable timing to measure just the overhead
        streamHighRate.setTimingEnabled(false);
        std::vector<SampleType> buffer(sampleCount);
        double timeWithoutSleep = TimingTestUtils::measureTimeMs([&]() {
            streamHighRate.readSamples(buffer.data(), sampleCount);
        });

        std::cout << "✓ Average time without timing: " << avgTimeNoTiming << " ms" << std::endl;
        std::cout << "✓ Time with timing infrastructure (no sleep): " << timeWithoutSleep << " ms" << std::endl;

        // Calculate overhead
        double overhead = timeWithoutSleep - avgTimeNoTiming;
        double overheadPercent = (overhead / avgTimeNoTiming) * 100.0;

        std::cout << "✓ Timing infrastructure overhead: " << overhead << " ms ("
                 << overheadPercent << "%)" << std::endl;

        // Verify overhead is reasonable (less than 50% increase)
        if (overheadPercent > 50.0) {
            std::cout << "❌ Timing infrastructure overhead too high: " << overheadPercent << "%" << std::endl;
            TimingTestUtils::cleanup(testFiles);
            return 1;
        }

        std::cout << "✓ Performance impact is acceptable" << std::endl;

        TimingTestUtils::cleanup(testFiles);
        return 0;

    } catch (const std::exception& e) {
        std::cout << "❌ Exception in performance impact test: " << e.what() << std::endl;
        TimingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Main test function
int run_timing_tests() {
    std::cout << "Running WAVIQStream timing simulation tests..." << std::endl;

    int failures = 0;

    failures += test_basic_timing_simulation();
    failures += test_timing_accuracy_sample_rates();
    failures += test_timing_control_methods();
    failures += test_timing_chunk_sizes();
    failures += test_timing_performance_impact();

    if (failures == 0) {
        std::cout << "\n🎉 All timing simulation tests passed!" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " timing test(s) failed!" << std::endl;
    }

    return failures;
}
