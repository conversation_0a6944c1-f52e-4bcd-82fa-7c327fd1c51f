#include "../wav_stream.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <cstring>
#include <cassert>

// Test utilities
class WAVTestUtils {
public:
    // Create a simple test WAV file
    static bool createTestWAV(const std::string& filename, 
                             uint32_t sampleRate = 44100,
                             uint16_t bitsPerSample = 16,
                             uint16_t numChannels = 2,
                             uint32_t numSamples = 1000) {
        
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        // Calculate sizes
        uint32_t dataSize = numSamples * numChannels * (bitsPerSample / 8);
        uint32_t fileSize = 36 + dataSize;
        
        // Write WAV header
        file.write("RIFF", 4);
        file.write(reinterpret_cast<const char*>(&fileSize), 4);
        file.write("WAVE", 4);
        file.write("fmt ", 4);
        
        uint32_t fmtSize = 16;
        file.write(reinterpret_cast<const char*>(&fmtSize), 4);
        
        uint16_t audioFormat = 1; // PCM
        file.write(reinterpret_cast<const char*>(&audioFormat), 2);
        file.write(reinterpret_cast<const char*>(&numChannels), 2);
        file.write(reinterpret_cast<const char*>(&sampleRate), 4);
        
        uint32_t byteRate = sampleRate * numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&byteRate), 4);
        
        uint16_t blockAlign = numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&blockAlign), 2);
        file.write(reinterpret_cast<const char*>(&bitsPerSample), 2);
        
        file.write("data", 4);
        file.write(reinterpret_cast<const char*>(&dataSize), 4);
        
        // Write sample data
        for (uint32_t i = 0; i < numSamples; ++i) {
            if (bitsPerSample == 16) {
                int16_t i_sample = static_cast<int16_t>(i % 32767);
                int16_t q_sample = static_cast<int16_t>((i * 2) % 32767);
                file.write(reinterpret_cast<const char*>(&i_sample), 2);
                file.write(reinterpret_cast<const char*>(&q_sample), 2);
            } else if (bitsPerSample == 32) {
                int32_t i_sample = static_cast<int32_t>(i % 2147483647);
                int32_t q_sample = static_cast<int32_t>((i * 2) % 2147483647);
                file.write(reinterpret_cast<const char*>(&i_sample), 4);
                file.write(reinterpret_cast<const char*>(&q_sample), 4);
            }
        }
        
        file.close();
        return true;
    }
    
    // Create an invalid WAV file for error testing
    static bool createInvalidWAV(const std::string& filename) {
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        // Write invalid header
        file.write("INVALID", 7);
        file.close();
        return true;
    }
    
    // Cleanup test files
    static void cleanup(const std::vector<std::string>& filenames) {
        for (const auto& filename : filenames) {
            std::remove(filename.c_str());
        }
    }
};

// Test basic WAV stream functionality
int test_basic_functionality() {
    std::cout << "\n--- Testing Basic Functionality ---" << std::endl;
    
    const std::string testFile = "test_basic.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test WAV file
        if (!WAVTestUtils::createTestWAV(testFile, 44100, 16, 2, 100)) {
            std::cout << "❌ Failed to create test WAV file" << std::endl;
            return 1;
        }
        
        // Test constructor
        WAVIQStream stream(testFile);
        std::cout << "✓ Constructor works" << std::endl;
        
        // Test open
        if (!stream.open()) {
            std::cout << "❌ Failed to open WAV file: " << stream.lastError() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ File opened successfully" << std::endl;
        
        // Test properties
        if (stream.sampleRate() != 44100) {
            std::cout << "❌ Incorrect sample rate: " << stream.sampleRate() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Sample rate correct: " << stream.sampleRate() << std::endl;
        
        if (stream.sourceName() != "wav") {
            std::cout << "❌ Incorrect source name: " << stream.sourceName() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Source name correct: " << stream.sourceName() << std::endl;
        
        if (!stream.isActive()) {
            std::cout << "❌ Stream should be active" << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Stream is active" << std::endl;
        
        // Test reading samples
        std::vector<SampleType> samples(10);
        if (!stream.readSamples(samples.data(), 10)) {
            std::cout << "❌ Failed to read samples: " << stream.lastError() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read 10 samples" << std::endl;

        // Verify sample format (0xQQQQIIII)
        SampleType firstSample = samples[0];
        uint16_t i_part = static_cast<uint16_t>(firstSample & 0xFFFF);
        uint16_t q_part = static_cast<uint16_t>((firstSample >> 16) & 0xFFFF);
        std::cout << "✓ Sample format: I=" << i_part << ", Q=" << q_part << std::endl;
        
        // Test close
        stream.close();
        if (stream.isActive()) {
            std::cout << "❌ Stream should not be active after close" << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Stream closed successfully" << std::endl;
        
        WAVTestUtils::cleanup(testFiles);
        std::cout << "✓ Basic functionality tests passed" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in basic functionality test: " << e.what() << std::endl;
        WAVTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test error handling
int test_error_handling() {
    std::cout << "\n--- Testing Error Handling ---" << std::endl;
    
    const std::string invalidFile = "test_invalid.wav";
    const std::string nonexistentFile = "nonexistent.wav";
    std::vector<std::string> testFiles = {invalidFile};
    
    try {
        // Test nonexistent file
        WAVIQStream stream1(nonexistentFile);
        if (stream1.open()) {
            std::cout << "❌ Should fail to open nonexistent file" << std::endl;
            return 1;
        }
        std::cout << "✓ Correctly failed to open nonexistent file" << std::endl;
        std::cout << "  Error: " << stream1.lastError() << std::endl;
        
        // Test invalid WAV file
        if (!WAVTestUtils::createInvalidWAV(invalidFile)) {
            std::cout << "❌ Failed to create invalid WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream2(invalidFile);
        if (stream2.open()) {
            std::cout << "❌ Should fail to open invalid WAV file" << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Correctly failed to open invalid WAV file" << std::endl;
        std::cout << "  Error: " << stream2.lastError() << std::endl;
        
        // Test reading from closed stream
        WAVIQStream stream3("dummy.wav");
        std::vector<SampleType> samples(10);
        if (stream3.readSamples(samples.data(), 10)) {
            std::cout << "❌ Should fail to read from unopened stream" << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Correctly failed to read from unopened stream" << std::endl;
        
        WAVTestUtils::cleanup(testFiles);
        std::cout << "✓ Error handling tests passed" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in error handling test: " << e.what() << std::endl;
        WAVTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test different WAV formats
int test_wav_formats() {
    std::cout << "\n--- Testing Different WAV Formats ---" << std::endl;
    
    const std::string file16bit = "test_16bit.wav";
    const std::string file32bit = "test_32bit.wav";
    std::vector<std::string> testFiles = {file16bit, file32bit};
    
    try {
        // Test 16-bit WAV
        if (!WAVTestUtils::createTestWAV(file16bit, 48000, 16, 2, 50)) {
            std::cout << "❌ Failed to create 16-bit test WAV" << std::endl;
            return 1;
        }
        
        WAVIQStream stream16(file16bit);
        if (!stream16.open()) {
            std::cout << "❌ Failed to open 16-bit WAV: " << stream16.lastError() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        if (stream16.sampleRate() != 48000) {
            std::cout << "❌ Incorrect 16-bit sample rate: " << stream16.sampleRate() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ 16-bit WAV format supported" << std::endl;
        
        // Test 32-bit WAV
        if (!WAVTestUtils::createTestWAV(file32bit, 96000, 32, 2, 50)) {
            std::cout << "❌ Failed to create 32-bit test WAV" << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        WAVIQStream stream32(file32bit);
        if (!stream32.open()) {
            std::cout << "❌ Failed to open 32-bit WAV: " << stream32.lastError() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        if (stream32.sampleRate() != 96000) {
            std::cout << "❌ Incorrect 32-bit sample rate: " << stream32.sampleRate() << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ 32-bit WAV format supported" << std::endl;
        
        // Test reading from both
        std::vector<SampleType> samples16(10), samples32(10);
        
        if (!stream16.readSamples(samples16.data(), 10)) {
            std::cout << "❌ Failed to read from 16-bit WAV" << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        if (!stream32.readSamples(samples32.data(), 10)) {
            std::cout << "❌ Failed to read from 32-bit WAV" << std::endl;
            WAVTestUtils::cleanup(testFiles);
            return 1;
        }
        
        std::cout << "✓ Successfully read from both 16-bit and 32-bit WAV files" << std::endl;
        
        WAVTestUtils::cleanup(testFiles);
        std::cout << "✓ WAV format tests passed" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in WAV format test: " << e.what() << std::endl;
        WAVTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Main test function
int run_wav_stream_tests() {
    std::cout << "Running WAVIQStream basic tests..." << std::endl;
    
    int failures = 0;
    
    failures += test_basic_functionality();
    failures += test_error_handling();
    failures += test_wav_formats();
    
    if (failures == 0) {
        std::cout << "\n🎉 All basic WAVIQStream tests passed!" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " test(s) failed!" << std::endl;
    }
    
    return failures;
}
