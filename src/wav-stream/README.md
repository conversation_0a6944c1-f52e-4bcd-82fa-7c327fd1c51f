# WAVIQStream Test Suite

Comprehensive test suite for the WAVIQStream implementation, following the same testing patterns as other components in the BladeRF video processing project.

## Overview

WAVIQStream is an implementation of the IIQStream interface that reads IQ samples from WAV files. It supports both 16-bit and 32-bit WAV files with 2 channels (I and Q), automatically converting samples to the standardized SampleType format (32-bit little-endian 0xQQQQIIII).

**NEW: Realistic Timing Simulation** - WAVIQStream now includes optional timing simulation that mimics real hardware IQ streams, preventing buffer overflow issues during development and testing by reading samples at the declared sample rate rather than as fast as possible.

## Test Structure

The test suite is organized into multiple test files, similar to the chunk-processor tests:

```
src/wav-stream/
├── wav_stream.h              # Header file
├── wav_stream.cpp            # Implementation
├── Makefile                  # Build configuration
├── run_tests.sh              # Test runner script
├── README.md                 # This file
└── tests/
    ├── test_runner.cpp       # Main test runner
    ├── test_wav_stream.cpp   # Basic functionality tests
    ├── comprehensive_tests.cpp # Advanced tests
    └── performance_tests.cpp # Performance benchmarks
```

## Features Tested

### Basic Functionality (`test_wav_stream.cpp`)
- ✅ WAV file opening and parsing
- ✅ Header validation
- ✅ Sample rate detection
- ✅ IQ sample reading
- ✅ Sample format verification (0xQQQQIIII)
- ✅ Error handling for invalid files
- ✅ Support for 16-bit and 32-bit WAV formats
- ✅ Stream state management

### Timing Simulation (`timing_tests.cpp`)
- ✅ Realistic hardware timing simulation
- ✅ Configurable timing enable/disable
- ✅ Accurate timing across different sample rates
- ✅ Variable chunk size timing validation
- ✅ Performance impact assessment
- ✅ Timing precision validation (±30% tolerance)

### Looping Functionality (`test_looping.cpp`)
- ✅ Cyclic reading (automatic restart at EOF)
- ✅ Looping enable/disable control
- ✅ Dynamic looping control during runtime
- ✅ Multiple loop cycles with pattern verification
- ✅ Manual reset functionality
- ✅ Total samples read counter across loops

### Comprehensive Tests (`comprehensive_tests.cpp`)
- ✅ Sample packing correctness
- ✅ End-of-stream behavior
- ✅ Multiple open/close cycles
- ✅ IIQStream interface compliance
- ✅ Pattern validation
- ✅ Resource management

### Performance Tests (`performance_tests.cpp`)
- ✅ File opening performance
- ✅ Reading throughput (>1M samples/sec)
- ✅ Memory usage with multiple streams
- ✅ Sample packing efficiency
- ✅ Different chunk size optimization

## Running Tests

### Quick Start
```bash
cd src/wav-stream
./run_tests.sh
```

### Using Makefile
```bash
# Run all tests
make test

# Quick verification
make verify

# Performance tests only
make perf

# Build without running
make build

# Clean up
make clean
```

### Test Runner Options
```bash
# Standard run
./run_tests.sh

# Quick verification mode
./run_tests.sh --quick

# Verbose output
./run_tests.sh --verbose

# Performance tests only
./run_tests.sh --perf

# Looping tests only
./run_tests.sh --loop

# Help
./run_tests.sh --help
```

## Test Results

When all tests pass, you should see:

```
🎉 All WAVIQStream tests completed successfully!

Key Features Verified:
  ✓ WAV file parsing and validation
  ✓ 16-bit and 32-bit sample support
  ✓ IQ sample packing (0xQQQQIIII format)
  ✓ Error handling and reporting
  ✓ IIQStream interface compliance
  ✓ File I/O operations
  ✓ Sample rate detection
  ✓ Multi-channel WAV support
```

## Performance Benchmarks

Expected performance characteristics:

| Metric | Target | Typical |
|--------|--------|---------|
| File Opening | <50ms | ~10ms |
| Reading Rate | >1M samples/sec | ~10M samples/sec |
| Memory Usage | Efficient | <2MB per stream |
| Throughput | High | ~40MB/sec |

## WAV File Requirements

The WAVIQStream supports WAV files with the following specifications:

- **Format**: PCM (uncompressed)
- **Channels**: 2 (stereo for I/Q data)
- **Bit Depth**: 16-bit or 32-bit
- **Sample Rate**: Any (detected automatically)
- **Byte Order**: Little-endian
- **Header**: Standard WAV/RIFF format

## Sample Format

IQ samples are packed into 32-bit words using the format:
```
0xQQQQIIII
```
Where:
- Lower 16 bits (IIII): I (in-phase) component
- Upper 16 bits (QQQQ): Q (quadrature) component

## Integration

WAVIQStream implements the IIQStream interface and can be used anywhere an IIQStream is expected:

```cpp
#include "wav_stream.h"

// Create and open WAV stream (no looping, no timing simulation)
WAVIQStream stream("recording.wav");
if (!stream.open()) {
    std::cerr << "Error: " << stream.lastError() << std::endl;
    return 1;
}

// Create with looping enabled
WAVIQStream loopingStream("recording.wav", true);
if (!loopingStream.open()) {
    std::cerr << "Error: " << loopingStream.lastError() << std::endl;
    return 1;
}

// Create with realistic timing simulation enabled
WAVIQStream realtimeStream("recording.wav", false, true); // looping=false, timing=true
if (!realtimeStream.open()) {
    std::cerr << "Error: " << realtimeStream.lastError() << std::endl;
    return 1;
}

// Read samples (will loop automatically if enabled)
std::vector<SampleType> samples(1000);
if (loopingStream.readSamples(samples.data(), 1000)) {
    // Process samples... will continue indefinitely with looping
}

// Dynamic looping control
loopingStream.setLooping(false);  // Disable looping
loopingStream.setLooping(true);   // Re-enable looping

// Dynamic timing control
realtimeStream.setTimingEnabled(false); // Switch to fast mode
realtimeStream.setTimingEnabled(true);  // Switch back to realistic timing

// Check looping status
if (loopingStream.isLooping()) {
    std::cout << "Looping is enabled" << std::endl;
}

// Check timing status
if (realtimeStream.isTimingEnabled()) {
    std::cout << "Realistic timing simulation is enabled" << std::endl;
}

// Get total samples read across all loops
uint64_t totalRead = loopingStream.getTotalSamplesRead();

// Manual reset to beginning
if (!loopingStream.reset()) {
    std::cerr << "Reset failed: " << loopingStream.lastError() << std::endl;
}

// Use through interface
std::unique_ptr<IIQStream> iqStream = std::make_unique<WAVIQStream>("file.wav", true);
```

## Error Handling

The implementation provides comprehensive error handling:

- File not found or inaccessible
- Invalid WAV header format
- Unsupported audio formats
- Incorrect channel count
- Read errors during operation
- End-of-stream detection

All errors are reported through the `lastError()` method.

## Dependencies

- C++17 compiler
- Standard library (no external dependencies)
- POSIX file I/O

## Build Requirements

```bash
# Compiler
g++ -std=c++17 -Wall -Wextra -O2

# No external libraries required
```

## Troubleshooting

### Build Issues
```bash
# Clean and rebuild
make clean
make build

# Check compiler version
g++ --version
```

### Test Failures
```bash
# Run with verbose output
./run_tests.sh --verbose

# Check specific test categories
make test 2>&1 | grep "❌"
```

### Performance Issues
```bash
# Run performance tests only
./run_tests.sh --perf

# Check system resources
htop
```

## Next Steps

After successful testing:

1. **Integration**: Use WAVIQStream in video processing pipeline
2. **Optimization**: Profile with real-world WAV files
3. **Features**: Add support for additional WAV formats if needed
4. **Documentation**: Update API documentation

The WAVIQStream test suite ensures reliable, high-performance WAV file processing for the BladeRF video decoding project.
