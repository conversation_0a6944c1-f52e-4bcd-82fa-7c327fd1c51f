#!/bin/bash

# Test runner script for WAVIQStream
# BladeRF Video Decoding Project - WAV Stream Module

set -e  # Exit on any error

echo "WAVIQStream Test Runner"
echo "======================="
echo "WAV file IQ stream implementation with comprehensive test coverage"
echo ""

# Check if we're in the right directory
if [ ! -f "wav_stream.h" ]; then
    echo "Error: Must be run from src/wav-stream directory"
    echo "Usage: cd src/wav-stream && ./run_tests.sh"
    exit 1
fi

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "\033[34m[INFO]\033[0m $message"
            ;;
        "SUCCESS")
            echo -e "\033[32m[SUCCESS]\033[0m $message"
            ;;
        "ERROR")
            echo -e "\033[31m[ERROR]\033[0m $message"
            ;;
        "WARNING")
            echo -e "\033[33m[WARNING]\033[0m $message"
            ;;
    esac
}

# Parse command line arguments
QUICK_MODE=false
VERBOSE_MODE=false
PERFORMANCE_ONLY=false
LOOPING_ONLY=false
HELP_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE_MODE=true
            shift
            ;;
        -p|--perf)
            PERFORMANCE_ONLY=true
            shift
            ;;
        -l|--loop)
            LOOPING_ONLY=true
            shift
            ;;
        -h|--help)
            HELP_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            HELP_MODE=true
            shift
            ;;
    esac
done

# Show help if requested
if [ "$HELP_MODE" = true ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -q, --quick    Quick verification mode (limited output)"
    echo "  -v, --verbose  Verbose mode (full output)"
    echo "  -p, --perf     Performance benchmarks only"
    echo "  -l, --loop     Looping functionality tests only"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run all tests with standard output"
    echo "  $0 --quick      # Quick verification"
    echo "  $0 --verbose    # Full verbose output"
    echo "  $0 --perf       # Performance tests only"
    echo "  $0 --loop       # Looping tests only"
    echo ""
    exit 0
fi

# Clean previous builds
print_status "INFO" "Cleaning previous builds..."
make clean > /dev/null 2>&1

# Validate implementation first
print_status "INFO" "Validating WAVIQStream implementation..."
if make validate > /dev/null 2>&1; then
    print_status "SUCCESS" "Implementation validation passed"
else
    print_status "ERROR" "Implementation validation failed"
    exit 1
fi

# Build tests
print_status "INFO" "Building WAVIQStream tests..."
if make build > /dev/null 2>&1; then
    print_status "SUCCESS" "Build completed successfully"
else
    print_status "ERROR" "Build failed"
    exit 1
fi

# Run tests based on mode
if [ "$PERFORMANCE_ONLY" = true ]; then
    print_status "INFO" "Running performance benchmarks only..."
    echo ""
    make perf
elif [ "$LOOPING_ONLY" = true ]; then
    print_status "INFO" "Running looping functionality tests only..."
    echo ""
    make loop
elif [ "$QUICK_MODE" = true ]; then
    print_status "INFO" "Running quick verification..."
    echo ""
    make verify
elif [ "$VERBOSE_MODE" = true ]; then
    print_status "INFO" "Running all tests in verbose mode..."
    echo ""
    make test
else
    print_status "INFO" "Running all tests with standard output..."
    echo ""
    make test
fi

# Capture exit code
TEST_EXIT_CODE=$?

echo ""
echo "============================="

if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_status "SUCCESS" "All WAVIQStream tests completed successfully!"
    echo ""
    echo "Key Features Verified:"
    echo "  ✓ WAV file parsing and validation"
    echo "  ✓ 16-bit and 32-bit sample support"
    echo "  ✓ IQ sample packing (0xQQQQIIII format)"
    echo "  ✓ Error handling and reporting"
    echo "  ✓ IIQStream interface compliance"
    echo "  ✓ File I/O operations"
    echo "  ✓ Sample rate detection"
    echo "  ✓ Multi-channel WAV support"
    echo "  ✓ Cyclic reading (looping) functionality"
    echo "  ✓ Dynamic looping control"
    echo "  ✓ Reset and restart capabilities"
    echo ""
    echo "Next Steps:"
    echo "  1. Build main project: npm run build"
    echo "  2. Integration testing with video processing pipeline"
    echo "  3. Use WAVIQStream in your applications"
    echo ""
else
    print_status "ERROR" "Some tests failed (exit code: $TEST_EXIT_CODE)"
    echo ""
    echo "Troubleshooting:"
    echo "  - Review test output above for specific failures"
    echo "  - Run with --verbose for detailed information"
    echo "  - Check WAV file format compliance"
    echo "  - Verify file permissions and accessibility"
    echo ""
fi

echo "============================="
exit $TEST_EXIT_CODE
