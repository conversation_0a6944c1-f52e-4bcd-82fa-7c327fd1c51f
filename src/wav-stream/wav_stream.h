#ifndef WAV_IQ_STREAM_H
#define WAV_IQ_STREAM_H

#include "../iiq-stream/iiq_stream.h"
#include <fstream>
#include <chrono>

/**
 * WAVIQStream - Implementation of IIQStream for WAV file sources
 * 
 * Reads IQ samples from WAV files. Supports both 16-bit and 32-bit WAV files
 * with 2 channels (I and Q). Automatically converts samples to the standardized
 * SampleType format (32-bit little-endian 0xQQQQIIII).
 */
class WAVIQStream : public IIQStream {
private:
    struct WAVHeader {
        char riff[4];           // "RIFF"
        uint32_t fileSize;      // File size - 8
        char wave[4];           // "WAVE"
        char fmt[4];            // "fmt "
        uint32_t fmtSize;       // Format chunk size
        uint16_t audioFormat;   // Audio format (1 = PCM)
        uint16_t numChannels;   // Number of channels
        uint32_t sampleRate;    // Sample rate
        uint32_t byteRate;      // Byte rate
        uint16_t blockAlign;    // Block align
        uint16_t bitsPerSample; // Bits per sample
        char data[4];           // "data"
        uint32_t dataSize;      // Data chunk size
    };

    std::ifstream file_;
    std::string filename_;
    std::string sourceName_;
    std::string lastError_;
    SampleRateType sampleRate_;
    uint16_t bitsPerSample_;
    uint16_t numChannels_;
    size_t dataStartPos_;
    size_t dataSize_;
    size_t currentPos_;
    bool isActive_;
    bool isOpen_;
    bool loopEnabled_;
    uint64_t totalSamplesRead_;

    // Timing simulation members
    bool timingEnabled_;
    double nanosPerSample_;  // Nanoseconds per sample for timing simulation

    /**
     * Parse and validate WAV header
     * @return true on success, false on error
     */
    bool parseHeader();

    /**
     * Convert 16-bit I,Q samples to SampleType format
     * @param i 16-bit I sample
     * @param q 16-bit Q sample
     * @return Combined 32-bit sample in 0xQQQQIIII format
     */
    inline SampleType packSample16(int16_t i, int16_t q) const noexcept {
        return static_cast<SampleType>(static_cast<uint16_t>(i)) | 
               (static_cast<SampleType>(static_cast<uint16_t>(q)) << 16);
    }

    /**
     * Convert 32-bit I,Q samples to SampleType format
     * @param i 32-bit I sample
     * @param q 32-bit Q sample
     * @return Combined 32-bit sample in 0xQQQQIIII format (scaled down)
     */
    inline SampleType packSample32(int32_t i, int32_t q) const noexcept {
        // Scale down from 32-bit to 16-bit range
        int16_t i16 = static_cast<int16_t>(i >> 16);
        int16_t q16 = static_cast<int16_t>(q >> 16);
        return packSample16(i16, q16);
    }

    /**
     * Set error message and return false
     * @param error Error message
     * @return false
     */
    bool setError(const std::string& error);

public:
    /**
     * Constructor
     * @param filename Path to WAV file
     * @param enableLoop Enable cyclic reading (restart from beginning when EOF reached)
     * @param enableTiming Enable realistic timing simulation (default: false for backward compatibility)
     */
    explicit WAVIQStream(const std::string& filename, bool enableLoop = false, bool enableTiming = false);

    /**
     * Destructor
     */
    ~WAVIQStream() override;

    /**
     * Open the WAV file and parse header
     * @return true on success, false on error
     */
    bool open();

    /**
     * Enable or disable looping
     * @param enable true to enable looping, false to disable
     */
    void setLooping(bool enable) noexcept;

    /**
     * Check if looping is enabled
     * @return true if looping is enabled
     */
    bool isLooping() const noexcept;

    /**
     * Get total number of samples read (including across loop cycles)
     * @return Total samples read
     */
    uint64_t getTotalSamplesRead() const noexcept;

    /**
     * Reset to beginning of file (useful for manual restart)
     * @return true on success, false on error
     */
    bool reset();

    /**
     * Enable or disable timing simulation
     * @param enable true to enable realistic timing, false to disable
     */
    void setTimingEnabled(bool enable) noexcept;

    /**
     * Check if timing simulation is enabled
     * @return true if timing simulation is enabled
     */
    bool isTimingEnabled() const noexcept;

    // IIQStream interface implementation
    bool readSamples(SampleType* dst, size_t sampleCount) override;
    SampleRateType sampleRate() const noexcept override;
    const std::string& sourceName() const noexcept override;
    bool isActive() const noexcept override;
    void close() noexcept override;
    const std::string& lastError() const noexcept override;
};

#endif // WAV_IQ_STREAM_H