#ifndef BLADERF_IQ_STREAM_H
#define BLADERF_IQ_STREAM_H

#include "../iiq-stream/iiq_stream.h"
#include <libbladeRF.h>
#include <vector>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <memory>

/**
 * BladeRFIQStream - Implementation of IIQStream for BladeRF device sources
 * 
 * Streams IQ samples from BladeRF devices in real-time. Handles device configuration,
 * buffering, and converts samples to the standardized SampleType format.
 */
class BladeRFIQStream : public IIQStream {
public:
    /**
     * Configuration structure for BladeRF parameters
     */
    struct Config {
        uint64_t frequency;     // Center frequency in Hz
        uint32_t sampleRate;    // Sample rate in Hz
        uint32_t bandwidth;     // Bandwidth in Hz
        int gain;               // Gain in dB
        
        Config(uint64_t freq = 915000000, uint32_t rate = 1000000, 
               uint32_t bw = 1500000, int g = 30)
            : frequency(freq), sampleRate(rate), bandwidth(bw), gain(g) {}
    };

private:
    struct bladerf* device_;
    std::string deviceIdentifier_;
    std::string sourceName_;
    std::string lastError_;
    Config config_;
    
    // Streaming state
    std::atomic<bool> isActive_;
    std::atomic<bool> isOpen_;
    std::atomic<bool> streamingActive_;
    
    // Buffer management
    static constexpr size_t BUFFER_SIZE = 1024;  // Samples per buffer (must be multiple of 1024)
    static constexpr size_t NUM_BUFFERS = 16;    // Number of circular buffers
    static constexpr size_t NUM_TRANSFERS = 8;   // Number of transfers
    static constexpr size_t TIMEOUT_MS = 3000;   // RX timeout
    
    std::unique_ptr<std::thread> streamingThread_;
    std::queue<std::vector<SampleType>> sampleQueue_;
    std::mutex queueMutex_;
    std::condition_variable queueCondition_;
    std::vector<SampleType> currentBuffer_;
    size_t currentBufferPos_;
    
    /**
     * Convert BladeRF int16_t I,Q samples to SampleType format
     * @param i 16-bit I sample (SC16_Q11 format)
     * @param q 16-bit Q sample (SC16_Q11 format)
     * @return Combined 32-bit sample in 0xQQQQIIII format
     */
    inline SampleType packSample(int16_t i, int16_t q) const noexcept {
        return static_cast<SampleType>(static_cast<uint16_t>(i)) |
               (static_cast<SampleType>(static_cast<uint16_t>(q)) << 16);
    }
    
    /**
     * Set error message and return false
     * @param error Error message
     * @return false
     */
    bool setError(const std::string& error);
    
    /**
     * Configure BladeRF device with specified parameters
     * @return true on success, false on error
     */
    bool configureDevice();
    
    /**
     * Streaming thread function
     */
    void streamingThreadFunction();
    
    /**
     * Start the streaming thread
     * @return true on success, false on error
     */
    bool startStreaming();
    
    /**
     * Stop the streaming thread
     */
    void stopStreaming();

public:
    /**
     * Constructor
     * @param config BladeRF configuration parameters
     * @param deviceIdentifier Device identifier (empty for first available)
     */
    explicit BladeRFIQStream(const Config& config = Config(), 
                            const std::string& deviceIdentifier = "");
    
    /**
     * Destructor
     */
    ~BladeRFIQStream() override;
    
    /**
     * Open the BladeRF device and start streaming
     * @return true on success, false on error
     */
    bool open();
    
    /**
     * Update device configuration (requires restart)
     * @param config New configuration
     * @return true on success, false on error
     */
    bool updateConfig(const Config& config);
    
    /**
     * Get current configuration
     * @return Current configuration
     */
    const Config& getConfig() const noexcept { return config_; }
    
    // IIQStream interface implementation
    bool readSamples(SampleType* dst, size_t sampleCount) override;
    SampleRateType sampleRate() const noexcept override;
    const std::string& sourceName() const noexcept override;
    bool isActive() const noexcept override;
    void close() noexcept override;
    const std::string& lastError() const noexcept override;
};

#endif // BLADERF_IQ_STREAM_H