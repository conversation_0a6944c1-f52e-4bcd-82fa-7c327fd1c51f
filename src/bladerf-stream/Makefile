# Makefile for BladeRFIQStream Tests
# BladeRF Video Decoding Project - BladeRF Stream Module

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I. -I./tests -I../

# Source directories
TEST_DIR = tests
SRC_DIR = .

# Source files for test executable
TEST_SOURCES = $(TEST_DIR)/test_bladerf_stream.cpp \
               $(TEST_DIR)/comprehensive_tests.cpp \
               $(TEST_DIR)/mock_tests.cpp \
               $(TEST_DIR)/performance_tests.cpp \
               $(TEST_DIR)/test_runner.cpp

# Implementation source files
IMPL_SOURCES = $(SRC_DIR)/bladerf_stream.cpp

# Object files
TEST_OBJECTS = $(TEST_SOURCES:.cpp=.o)
IMPL_OBJECTS = $(IMPL_SOURCES:.cpp=.o)

# Test executable
TEST_EXECUTABLE = bladerf_stream_tests

# BladeRF library
BLADERF_LIBS = -lbladeRF

# Default target - build and run tests
all: test

# Build the test executable
$(TEST_EXECUTABLE): $(TEST_OBJECTS) $(IMPL_OBJECTS)
	$(CXX) $(CXXFLAGS) -o $@ $^ $(BLADERF_LIBS)

# Object file compilation
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Build and run tests in one command
test: $(TEST_EXECUTABLE)
	@echo "Running all BladeRFIQStream tests..."
	@echo "=================================="
	./$(TEST_EXECUTABLE)

# Build only (without running)
build: $(TEST_EXECUTABLE)
	@echo "BladeRFIQStream test executable built: $(TEST_EXECUTABLE)"

# Quick verification (build and run with limited output)
verify: $(TEST_EXECUTABLE)
	@echo "Quick verification of BladeRFIQStream..."
	@echo "======================================="
	@./$(TEST_EXECUTABLE) | grep -E "(PASS|FAIL|===|🎉|❌)" | head -15
	@echo "======================================="
	@echo "Run 'make test' for full output"

# Performance test (run only performance benchmarks)
perf: $(TEST_EXECUTABLE)
	@echo "Running BladeRFIQStream performance tests..."
	@echo "============================================"
	@./$(TEST_EXECUTABLE) | grep -A 20 "PERFORMANCE BENCHMARK"

# Mock tests only (no hardware required)
mock: $(TEST_EXECUTABLE)
	@echo "Running BladeRFIQStream mock tests..."
	@echo "===================================="
	@./$(TEST_EXECUTABLE) --mock

# Hardware tests only (requires BladeRF device)
hardware: $(TEST_EXECUTABLE)
	@echo "Running BladeRFIQStream hardware tests..."
	@echo "========================================"
	@./$(TEST_EXECUTABLE) --hardware

# Clean build artifacts
clean:
	rm -f $(TEST_OBJECTS) $(IMPL_OBJECTS)
	rm -f $(TEST_EXECUTABLE)
	rm -f *.o tests/*.o

# Clean everything
distclean: clean

# Install dependencies
deps:
	@echo "Installing BladeRF dependencies..."
	@echo "=================================="
	@echo "Required packages:"
	@echo "  - libbladerf-dev"
	@echo "  - libbladerf2"
	@echo ""
	@echo "Install with:"
	@echo "  sudo apt-get install libbladerf-dev libbladerf2"

# Validate implementation
validate:
	@echo "Validating BladeRFIQStream implementation..."
	@echo "==========================================="
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only bladerf_stream.h bladerf_stream.cpp
	@echo "✓ Implementation syntax validation passed"

# Code analysis
analyze: $(TEST_EXECUTABLE)
	@echo "Running static analysis on BladeRFIQStream..."
	@echo "============================================="
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -Wall -Wextra -Wpedantic -fsyntax-only bladerf_stream.h bladerf_stream.cpp
	@echo "✓ Static analysis completed"

# Memory check (if valgrind is available)
memcheck: $(TEST_EXECUTABLE)
	@echo "Running memory check on BladeRFIQStream..."
	@echo "=========================================="
	@if command -v valgrind >/dev/null 2>&1; then \
		valgrind --leak-check=full --show-leak-kinds=all ./$(TEST_EXECUTABLE) --mock; \
	else \
		echo "Valgrind not available - skipping memory check"; \
		echo "Install valgrind for detailed memory analysis"; \
	fi

# Help target
help:
	@echo "BladeRFIQStream Test Makefile"
	@echo "============================="
	@echo "Available targets:"
	@echo "  test           - Build and run all tests (default)"
	@echo "  build          - Build test executable only"
	@echo "  verify         - Quick verification with limited output"
	@echo "  perf           - Run performance benchmarks only"
	@echo "  mock           - Run mock tests only (no hardware)"
	@echo "  hardware       - Run hardware tests only (requires BladeRF)"
	@echo "  validate       - Validate implementation syntax"
	@echo "  analyze        - Run static code analysis"
	@echo "  memcheck       - Run memory leak detection (requires valgrind)"
	@echo "  clean          - Remove build artifacts"
	@echo "  deps           - Show dependency installation instructions"
	@echo "  help           - Show this help message"
	@echo ""
	@echo "Simple usage:"
	@echo "  make test      # Build and run all tests"
	@echo "  make mock      # Test without hardware"
	@echo "  make hardware  # Test with BladeRF device"
	@echo "  make clean     # Clean up"
	@echo ""
	@echo "BladeRFIQStream Features:"
	@echo "  - Real-time BladeRF device streaming"
	@echo "  - Device configuration and control"
	@echo "  - Multi-threaded sample buffering"
	@echo "  - IQ sample packing (0xQQQQIIII format)"
	@echo "  - Error handling and reporting"
	@echo "  - IIQStream interface compliance"

# Phony targets
.PHONY: all test build verify perf mock hardware clean distclean deps validate analyze memcheck help

# Dependency tracking
$(TEST_DIR)/test_bladerf_stream.o: $(TEST_DIR)/test_bladerf_stream.cpp bladerf_stream.h ../iiq-stream/iiq_stream.h ../types.h

$(TEST_DIR)/comprehensive_tests.o: $(TEST_DIR)/comprehensive_tests.cpp bladerf_stream.h ../iiq-stream/iiq_stream.h ../types.h

$(TEST_DIR)/mock_tests.o: $(TEST_DIR)/mock_tests.cpp bladerf_stream.h ../iiq-stream/iiq_stream.h ../types.h

$(TEST_DIR)/performance_tests.o: $(TEST_DIR)/performance_tests.cpp bladerf_stream.h ../iiq-stream/iiq_stream.h ../types.h

$(TEST_DIR)/test_runner.o: $(TEST_DIR)/test_runner.cpp

$(SRC_DIR)/bladerf_stream.o: $(SRC_DIR)/bladerf_stream.cpp bladerf_stream.h ../iiq-stream/iiq_stream.h ../types.h

# Additional compiler flags for different build types
debug: CXXFLAGS += -DDEBUG -O0 -g3
debug: $(TEST_EXECUTABLE)

release: CXXFLAGS += -DNDEBUG -O3 -flto
release: $(TEST_EXECUTABLE)
