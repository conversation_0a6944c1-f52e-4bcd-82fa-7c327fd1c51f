#!/bin/bash

# Test runner script for BladeRFIQStream
# BladeRF Video Decoding Project - BladeRF Stream Module

set -e  # Exit on any error

echo "BladeRFIQStream Test Runner"
echo "==========================="
echo "Real-time BladeRF device streaming implementation with comprehensive test coverage"
echo ""

# Check if we're in the right directory
if [ ! -f "bladerf_stream.h" ]; then
    echo "Error: Must be run from src/bladerf-stream directory"
    echo "Usage: cd src/bladerf-stream && ./run_tests.sh"
    exit 1
fi

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "\033[34m[INFO]\033[0m $message"
            ;;
        "SUCCESS")
            echo -e "\033[32m[SUCCESS]\033[0m $message"
            ;;
        "ERROR")
            echo -e "\033[31m[ERROR]\033[0m $message"
            ;;
        "WARNING")
            echo -e "\033[33m[WARNING]\033[0m $message"
            ;;
    esac
}

# Parse command line arguments
QUICK_MODE=false
VERBOSE_MODE=false
PERFORMANCE_ONLY=false
MOCK_ONLY=false
HARDWARE_ONLY=false
HELP_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE_MODE=true
            shift
            ;;
        -p|--perf)
            PERFORMANCE_ONLY=true
            shift
            ;;
        -m|--mock)
            MOCK_ONLY=true
            shift
            ;;
        -w|--hardware)
            HARDWARE_ONLY=true
            shift
            ;;
        -h|--help)
            HELP_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            HELP_MODE=true
            shift
            ;;
    esac
done

# Show help if requested
if [ "$HELP_MODE" = true ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -q, --quick    Quick verification mode (limited output)"
    echo "  -v, --verbose  Verbose mode (full output)"
    echo "  -p, --perf     Performance benchmarks only"
    echo "  -m, --mock     Mock tests only (no hardware required)"
    echo "  -w, --hardware Hardware tests only (requires BladeRF device)"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run all tests with standard output"
    echo "  $0 --quick      # Quick verification"
    echo "  $0 --verbose    # Full verbose output"
    echo "  $0 --mock       # Mock tests only (safe without hardware)"
    echo "  $0 --hardware   # Hardware tests only (requires BladeRF)"
    echo "  $0 --perf       # Performance tests only"
    echo ""
    exit 0
fi

# Check BladeRF dependencies
print_status "INFO" "Checking BladeRF dependencies..."
if ! pkg-config --exists libbladeRF; then
    print_status "WARNING" "libbladeRF not found via pkg-config"
    print_status "INFO" "Checking for library files directly..."
    if [ ! -f "/usr/lib/libbladeRF.so" ] && [ ! -f "/usr/local/lib/libbladeRF.so" ]; then
        print_status "ERROR" "BladeRF library not found"
        print_status "INFO" "Install with: sudo apt-get install libbladerf-dev libbladerf2"
        exit 1
    fi
fi
print_status "SUCCESS" "BladeRF dependencies found"

# Clean previous builds
print_status "INFO" "Cleaning previous builds..."
make clean > /dev/null 2>&1

# Validate implementation first
print_status "INFO" "Validating BladeRFIQStream implementation..."
if make validate > /dev/null 2>&1; then
    print_status "SUCCESS" "Implementation validation passed"
else
    print_status "ERROR" "Implementation validation failed"
    exit 1
fi

# Build tests
print_status "INFO" "Building BladeRFIQStream tests..."
if make build > /dev/null 2>&1; then
    print_status "SUCCESS" "Build completed successfully"
else
    print_status "ERROR" "Build failed"
    exit 1
fi

# Run tests based on mode
if [ "$PERFORMANCE_ONLY" = true ]; then
    print_status "INFO" "Running performance benchmarks only..."
    echo ""
    make perf
elif [ "$MOCK_ONLY" = true ]; then
    print_status "INFO" "Running mock tests only (no hardware required)..."
    echo ""
    make mock
elif [ "$HARDWARE_ONLY" = true ]; then
    print_status "INFO" "Running hardware tests only (requires BladeRF device)..."
    echo ""
    make hardware
elif [ "$QUICK_MODE" = true ]; then
    print_status "INFO" "Running quick verification..."
    echo ""
    make verify
elif [ "$VERBOSE_MODE" = true ]; then
    print_status "INFO" "Running all tests in verbose mode..."
    echo ""
    make test
else
    print_status "INFO" "Running all tests with standard output..."
    echo ""
    make test
fi

# Capture exit code
TEST_EXIT_CODE=$?

echo ""
echo "============================="

if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_status "SUCCESS" "All BladeRFIQStream tests completed successfully!"
    echo ""
    echo "Key Features Verified:"
    echo "  ✓ BladeRF device detection and opening"
    echo "  ✓ Device configuration (frequency, sample rate, gain)"
    echo "  ✓ Real-time sample streaming"
    echo "  ✓ Multi-threaded buffer management"
    echo "  ✓ IQ sample packing (0xQQQQIIII format)"
    echo "  ✓ Error handling and reporting"
    echo "  ✓ IIQStream interface compliance"
    echo "  ✓ Thread safety and resource management"
    echo ""
    echo "Next Steps:"
    echo "  1. Build main project: npm run build"
    echo "  2. Integration testing with video processing pipeline"
    echo "  3. Use BladeRFIQStream with real BladeRF devices"
    echo ""
else
    print_status "ERROR" "Some tests failed (exit code: $TEST_EXIT_CODE)"
    echo ""
    echo "Troubleshooting:"
    echo "  - Review test output above for specific failures"
    echo "  - Run with --verbose for detailed information"
    echo "  - Check BladeRF device connection and permissions"
    echo "  - Try --mock mode to test without hardware"
    echo "  - Verify BladeRF library installation"
    echo ""
fi

echo "============================="
exit $TEST_EXIT_CODE
