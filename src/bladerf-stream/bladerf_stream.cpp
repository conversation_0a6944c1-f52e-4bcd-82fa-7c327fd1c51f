#include "bladerf_stream.h"
#include <iostream>
#include <algorithm>
#include <chrono>

BladeRFIQStream::BladeRFIQStream(const Config& config, const std::string& deviceIdentifier)
    : device_(nullptr)
    , deviceIdentifier_(deviceIdentifier)
    , sourceName_("bladeRF")
    , config_(config)
    , isActive_(false)
    , isOpen_(false)
    , streamingActive_(false)
    , currentBufferPos_(0) {
}

BladeRFIQStream::~BladeRFIQStream() {
    close();
}

bool BladeRFIQStream::open() {
    if (isOpen_) {
        close();
    }

    // Suppress BladeRF library info messages about firmware/FPGA version compatibility
    bladerf_log_set_verbosity(BLADERF_LOG_LEVEL_WARNING);

    // Open BladeRF device
    const char* dev_id = deviceIdentifier_.empty() ? nullptr : deviceIdentifier_.c_str();
    int status = bladerf_open(&device_, dev_id);
    
    if (status != 0) {
        return setError("Failed to open BladeRF device: " + std::string(bladerf_strerror(status)));
    }

    // Configure device
    if (!configureDevice()) {
        bladerf_close(device_);
        device_ = nullptr;
        return false;
    }

    // Start streaming
    if (!startStreaming()) {
        bladerf_close(device_);
        device_ = nullptr;
        return false;
    }

    isOpen_ = true;
    isActive_ = true;
    lastError_.clear();
    
    return true;
}

bool BladeRFIQStream::configureDevice() {
    if (!device_) {
        return setError("Device not open");
    }

    int status;

    // Set frequency
    status = bladerf_set_frequency(device_, BLADERF_CHANNEL_RX(0), config_.frequency);
    if (status != 0) {
        return setError("Failed to set frequency: " + std::string(bladerf_strerror(status)));
    }

    // Set sample rate
    uint32_t actual_rate;
    status = bladerf_set_sample_rate(device_, BLADERF_CHANNEL_RX(0), config_.sampleRate, &actual_rate);
    if (status != 0) {
        return setError("Failed to set sample rate: " + std::string(bladerf_strerror(status)));
    }

    // Update config with actual rate
    config_.sampleRate = actual_rate;

    // Set bandwidth
    uint32_t actual_bandwidth;
    status = bladerf_set_bandwidth(device_, BLADERF_CHANNEL_RX(0), config_.bandwidth, &actual_bandwidth);
    if (status != 0) {
        return setError("Failed to set bandwidth: " + std::string(bladerf_strerror(status)));
    }

    // Set gain
    status = bladerf_set_gain(device_, BLADERF_CHANNEL_RX(0), config_.gain);
    if (status != 0) {
        return setError("Failed to set gain: " + std::string(bladerf_strerror(status)));
    }

    return true;
}

bool BladeRFIQStream::startStreaming() {
    if (!device_) {
        return setError("Device not open");
    }

    // Configure sync interface
    int status = bladerf_sync_config(device_, BLADERF_RX_X1, BLADERF_FORMAT_SC16_Q11,
                                    NUM_BUFFERS, BUFFER_SIZE, NUM_TRANSFERS, TIMEOUT_MS);
    if (status != 0) {
        return setError("Failed to configure sync interface: " + std::string(bladerf_strerror(status)));
    }

    // Enable RX module
    status = bladerf_enable_module(device_, BLADERF_CHANNEL_RX(0), true);
    if (status != 0) {
        return setError("Failed to enable RX module: " + std::string(bladerf_strerror(status)));
    }

    // Start streaming thread
    streamingActive_ = true;
    streamingThread_ = std::make_unique<std::thread>(&BladeRFIQStream::streamingThreadFunction, this);

    return true;
}

void BladeRFIQStream::stopStreaming() {
    streamingActive_ = false;
    
    if (streamingThread_ && streamingThread_->joinable()) {
        streamingThread_->join();
    }
    streamingThread_.reset();

    if (device_) {
        bladerf_enable_module(device_, BLADERF_CHANNEL_RX(0), false);
    }
}

void BladeRFIQStream::streamingThreadFunction() {
    std::vector<int16_t> rx_buffer(BUFFER_SIZE * 2); // Interleaved I/Q
    
    while (streamingActive_ && device_) {
        // Receive IQ samples from BladeRF
        int status = bladerf_sync_rx(device_, rx_buffer.data(), BUFFER_SIZE, nullptr, TIMEOUT_MS);
        
        if (status != 0) {
            if (status == BLADERF_ERR_TIMEOUT) {
                continue; // Timeout is normal, continue streaming
            } else {
                setError("RX failed: " + std::string(bladerf_strerror(status)));
                streamingActive_ = false;
                isActive_ = false;
                break;
            }
        }

        // Convert to SampleType format
        std::vector<SampleType> converted_samples(BUFFER_SIZE);
        for (size_t i = 0; i < BUFFER_SIZE; ++i) {
            int16_t i_sample = rx_buffer[i * 2];     // I component
            int16_t q_sample = rx_buffer[i * 2 + 1]; // Q component
            converted_samples[i] = packSample(i_sample, q_sample);
        }

        // Add to queue (with size limit to prevent memory overflow)
        {
            std::lock_guard<std::mutex> lock(queueMutex_);
            
            // Limit queue size to prevent excessive memory usage
            while (sampleQueue_.size() >= NUM_BUFFERS) {
                sampleQueue_.pop(); // Remove oldest buffer
            }
            
            sampleQueue_.push(std::move(converted_samples));
        }
        
        queueCondition_.notify_one();
    }
}

bool BladeRFIQStream::readSamples(SampleType* dst, size_t sampleCount) {
    if (!isActive_ || !isOpen_) {
        return false;
    }

    if (sampleCount == 0) {
        return true;
    }

    size_t samplesRead = 0;
    
    while (samplesRead < sampleCount && isActive_) {
        // If current buffer is empty or exhausted, get a new one
        if (currentBuffer_.empty() || currentBufferPos_ >= currentBuffer_.size()) {
            std::unique_lock<std::mutex> lock(queueMutex_);
            
            // Wait for data with timeout
            if (!queueCondition_.wait_for(lock, std::chrono::milliseconds(TIMEOUT_MS), 
                                         [this] { return !sampleQueue_.empty() || !streamingActive_; })) {
                // Timeout - check if streaming is still active
                if (!streamingActive_) {
                    isActive_ = false;
                    return false;
                }
                continue; // Try again
            }
            
            if (sampleQueue_.empty()) {
                if (!streamingActive_) {
                    isActive_ = false;
                    return false;
                }
                continue;
            }
            
            currentBuffer_ = std::move(sampleQueue_.front());
            sampleQueue_.pop();
            currentBufferPos_ = 0;
        }
        
        // Copy samples from current buffer
        size_t availableSamples = currentBuffer_.size() - currentBufferPos_;
        size_t samplesToCopy = std::min(availableSamples, sampleCount - samplesRead);
        
        std::copy(currentBuffer_.begin() + currentBufferPos_,
                 currentBuffer_.begin() + currentBufferPos_ + samplesToCopy,
                 dst + samplesRead);
        
        currentBufferPos_ += samplesToCopy;
        samplesRead += samplesToCopy;
    }
    
    return samplesRead > 0;
}

bool BladeRFIQStream::updateConfig(const Config& config) {
    // If device is not open, just update config
    if (!isOpen_ || !device_) {
        config_ = config;
        return true;
    }

    // For open device, update parameters individually without full restart
    int status;

    // Update frequency if changed
    if (config.frequency != config_.frequency) {
        status = bladerf_set_frequency(device_, BLADERF_CHANNEL_RX(0), config.frequency);
        if (status != 0) {
            return setError("Failed to update frequency: " + std::string(bladerf_strerror(status)));
        }
    }

    // Update sample rate if changed
    if (config.sampleRate != config_.sampleRate) {
        uint32_t actual_rate;
        status = bladerf_set_sample_rate(device_, BLADERF_CHANNEL_RX(0), config.sampleRate, &actual_rate);
        if (status != 0) {
            return setError("Failed to update sample rate: " + std::string(bladerf_strerror(status)));
        }
        // Note: We'll update config_.sampleRate with actual_rate below
    }

    // Update bandwidth if changed
    if (config.bandwidth != config_.bandwidth) {
        uint32_t actual_bandwidth;
        status = bladerf_set_bandwidth(device_, BLADERF_CHANNEL_RX(0), config.bandwidth, &actual_bandwidth);
        if (status != 0) {
            return setError("Failed to update bandwidth: " + std::string(bladerf_strerror(status)));
        }
    }

    // Update gain if changed
    if (config.gain != config_.gain) {
        status = bladerf_set_gain(device_, BLADERF_CHANNEL_RX(0), config.gain);
        if (status != 0) {
            return setError("Failed to update gain: " + std::string(bladerf_strerror(status)));
        }
    }

    // Update our config with the new values
    config_ = config;

    // Get actual sample rate in case it was adjusted
    uint32_t actual_rate;
    status = bladerf_get_sample_rate(device_, BLADERF_CHANNEL_RX(0), &actual_rate);
    if (status == 0) {
        config_.sampleRate = actual_rate;
    }

    return true;
}

SampleRateType BladeRFIQStream::sampleRate() const noexcept {
    return config_.sampleRate;
}

const std::string& BladeRFIQStream::sourceName() const noexcept {
    return sourceName_;
}

bool BladeRFIQStream::isActive() const noexcept {
    return isActive_ && isOpen_ && streamingActive_;
}

void BladeRFIQStream::close() noexcept {
    isActive_ = false;
    
    stopStreaming();
    
    if (device_) {
        bladerf_close(device_);
        device_ = nullptr;
    }
    
    // Clear buffers
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        while (!sampleQueue_.empty()) {
            sampleQueue_.pop();
        }
        currentBuffer_.clear();
        currentBufferPos_ = 0;
    }
    
    isOpen_ = false;
}

const std::string& BladeRFIQStream::lastError() const noexcept {
    return lastError_;
}

bool BladeRFIQStream::setError(const std::string& error) {
    lastError_ = error;
    isActive_ = false;
    return false;
}