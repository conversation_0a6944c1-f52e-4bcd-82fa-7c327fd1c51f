# BladeRFIQStream Test Suite

Comprehensive test suite for the BladeRFIQStream implementation, following the same testing patterns as other components in the BladeRF video processing project.

## Overview

BladeRFIQStream is an implementation of the IIQStream interface that provides real-time streaming from BladeRF devices. It supports device configuration, multi-threaded sample buffering, and automatic IQ sample packing to the standardized SampleType format (32-bit little-endian 0xQQQQIIII).

## Test Structure

The test suite is organized into multiple test files, similar to the chunk-processor and wav-stream tests:

```
src/bladerf-stream/
├── bladerf_stream.h          # Header file
├── bladerf_stream.cpp        # Implementation
├── Makefile                  # Build configuration
├── run_tests.sh              # Test runner script
├── README.md                 # This file
└── tests/
    ├── test_runner.cpp       # Main test runner
    ├── mock_tests.cpp        # Tests without hardware
    ├── test_bladerf_stream.cpp # Hardware-dependent tests
    ├── comprehensive_tests.cpp # Advanced tests
    └── performance_tests.cpp  # Performance benchmarks
```

## Features Tested

### Mock Tests (`mock_tests.cpp`) - No Hardware Required
- ✅ Basic construction and destruction
- ✅ Configuration management and validation
- ✅ Interface compliance (IIQStream)
- ✅ Error handling without hardware
- ✅ Thread safety concepts
- ✅ Sample packing logic verification

### Hardware Tests (`test_bladerf_stream.cpp`) - Requires BladeRF Device
- ✅ Device detection and opening
- ✅ Device configuration (frequency, sample rate, gain)
- ✅ Real-time sample reading
- ✅ Sample format verification (0xQQQQIIII)
- ✅ Error handling with hardware
- ✅ Resource management

### Comprehensive Tests (`comprehensive_tests.cpp`)
- ✅ IIQStream interface compliance
- ✅ Multiple stream instance management
- ✅ Configuration edge cases
- ✅ Streaming behavior patterns
- ✅ Resource management and cleanup

### Performance Tests (`performance_tests.cpp`)
- ✅ Device opening performance (<1000ms)
- ✅ Reading throughput (>100K samples/sec)
- ✅ Sustained throughput over time
- ✅ Configuration change performance
- ✅ Memory usage with multiple streams

## Running Tests

### Quick Start
```bash
cd src/bladerf-stream
./run_tests.sh
```

### Using Makefile
```bash
# Run all tests
make test

# Quick verification
make verify

# Mock tests only (no hardware)
make mock

# Hardware tests only (requires BladeRF)
make hardware

# Performance tests only
make perf

# Build without running
make build

# Clean up
make clean
```

### Test Runner Options
```bash
# Standard run
./run_tests.sh

# Quick verification mode
./run_tests.sh --quick

# Verbose output
./run_tests.sh --verbose

# Mock tests only (safe without hardware)
./run_tests.sh --mock

# Hardware tests only (requires BladeRF)
./run_tests.sh --hardware

# Performance tests only
./run_tests.sh --perf

# Help
./run_tests.sh --help
```

## Test Results

When all tests pass, you should see:

```
🎉 All BladeRFIQStream tests completed successfully!

Key Features Verified:
  ✓ BladeRF device detection and opening
  ✓ Device configuration (frequency, sample rate, gain)
  ✓ Real-time sample streaming
  ✓ Multi-threaded buffer management
  ✓ IQ sample packing (0xQQQQIIII format)
  ✓ Error handling and reporting
  ✓ IIQStream interface compliance
  ✓ Thread safety and resource management
```

## Performance Benchmarks

Expected performance characteristics:

| Metric | Target | Typical |
|--------|--------|---------|
| Device Opening | <1000ms | ~500ms |
| Reading Rate | >100K samples/sec | ~1M samples/sec |
| Sustained Throughput | Stable | >95% success rate |
| Configuration Changes | <1000ms | ~100ms |
| Memory Usage | Efficient | <10MB per stream |

## BladeRF Requirements

The BladeRFIQStream requires:

- **Hardware**: BladeRF device (any model)
- **Drivers**: BladeRF drivers and firmware
- **Library**: libbladeRF development files
- **Permissions**: USB device access permissions

### Installation
```bash
# Install BladeRF library
sudo apt-get install libbladerf-dev libbladerf2

# Verify installation
pkg-config --libs --cflags libbladeRF
```

## Device Configuration

BladeRFIQStream supports configurable parameters:

```cpp
BladeRFIQStream::Config config(
    915000000,  // Frequency (Hz)
    1000000,    // Sample rate (Hz)
    1500000,    // Bandwidth (Hz)
    30          // Gain (dB)
);
```

### Supported Ranges
- **Frequency**: 70 MHz - 6 GHz (model dependent)
- **Sample Rate**: 160 kHz - 40 MHz
- **Bandwidth**: 200 kHz - 56 MHz
- **Gain**: 0 - 76 dB

## Sample Format

IQ samples are packed into 32-bit words using the format:
```
0xQQQQIIII
```
Where:
- Lower 16 bits (IIII): I (in-phase) component
- Upper 16 bits (QQQQ): Q (quadrature) component

## Integration

BladeRFIQStream implements the IIQStream interface and can be used anywhere an IIQStream is expected:

```cpp
#include "bladerf_stream.h"

// Create and configure stream
BladeRFIQStream::Config config(915000000, 1000000, 1500000, 30);
BladeRFIQStream stream(config);

if (!stream.open()) {
    std::cerr << "Error: " << stream.lastError() << std::endl;
    return 1;
}

// Read samples
std::vector<SampleType> samples(1000);
if (stream.readSamples(samples.data(), 1000)) {
    // Process samples...
}

// Use through interface
std::unique_ptr<IIQStream> iqStream = std::make_unique<BladeRFIQStream>(config);
```

## Error Handling

The implementation provides comprehensive error handling:

- Device not found or inaccessible
- Device already in use
- Invalid configuration parameters
- Streaming errors during operation
- Hardware communication failures

All errors are reported through the `lastError()` method.

## Dependencies

- C++17 compiler
- BladeRF library (libbladeRF)
- POSIX threads
- Standard library

## Build Requirements

```bash
# Compiler and BladeRF library
g++ -std=c++17 -Wall -Wextra -O2 -lbladeRF

# Check BladeRF library
pkg-config --exists libbladeRF
```

## Troubleshooting

### Build Issues
```bash
# Install BladeRF development files
sudo apt-get install libbladerf-dev

# Clean and rebuild
make clean
make build

# Check library installation
pkg-config --libs --cflags libbladeRF
```

### Hardware Issues
```bash
# Check device detection
bladeRF-cli -p

# Check permissions
sudo usermod -a -G plugdev $USER
# (logout and login required)

# Run mock tests only
./run_tests.sh --mock
```

### Performance Issues
```bash
# Run performance tests only
./run_tests.sh --perf

# Check system resources
htop

# Monitor USB bandwidth
lsusb -v
```

## Next Steps

After successful testing:

1. **Integration**: Use BladeRFIQStream in video processing pipeline
2. **Optimization**: Tune buffer sizes for specific applications
3. **Features**: Add support for additional BladeRF features
4. **Monitoring**: Implement streaming statistics and monitoring

The BladeRFIQStream test suite ensures reliable, high-performance real-time streaming from BladeRF devices for the video decoding project.
