#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <random>
#include <chrono>
#include "../find_front_frwd_no_ave.h"

using IQVideoProcessor::SignalProcessing::FindFrontFrwdNoAve;

// Test result tracking
struct TestResult {
  std::string name;
  bool passed;
  std::string error;
};

std::vector<TestResult> testResults;

// Helper functions
static std::vector<float> makeStep(size_t total, size_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (size_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static std::vector<float> makeSineWave(size_t total, float amplitude, float frequency) {
  std::vector<float> v(total);
  for (size_t i = 0; i < total; ++i) {
    v[i] = amplitude * std::sin(2.0f * M_PI * frequency * static_cast<float>(i) / static_cast<float>(total));
  }
  return v;
}

static std::vector<float> makeTriangleWave(size_t total, float amplitude, size_t period) {
  std::vector<float> v(total);
  for (size_t i = 0; i < total; ++i) {
    size_t pos = i % period;
    if (pos < period / 2) {
      v[i] = amplitude * (2.0f * static_cast<float>(pos) / static_cast<float>(period) - 1.0f);
    } else {
      v[i] = amplitude * (3.0f - 2.0f * static_cast<float>(pos) / static_cast<float>(period));
    }
  }
  return v;
}

// Test execution helper
#define RUN_TEST(test_func) do { \
  try { \
    test_func(); \
    testResults.push_back({#test_func, true, ""}); \
  } catch (const std::exception& e) { \
    testResults.push_back({#test_func, false, e.what()}); \
  } \
} while(0)

// Complex waveform tests
void test_sine_wave_fronts() {
  const size_t total = 2000;
  auto data = makeSineWave(total, 2.0f, 5.0f); // 5 cycles
  
  FindFrontFrwdNoAve<float> finder(data.data(), total);
  float pos = 0.0f; bool found = false;
  
  // Look for rising fronts in sine wave
  bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
  
  if (!ok) {
    throw std::runtime_error("Sine wave test execution failed");
  }
  
  // May or may not find fronts in sine wave depending on threshold
  // Just ensure it doesn't crash - sine waves may not have sharp enough fronts
  
  std::cout << "✓ sine wave fronts" << std::endl;
}

// Triangle wave tests
void test_triangle_wave_fronts() {
  const size_t total = 1000;
  auto data = makeTriangleWave(total, 3.0f, 200); // 5 cycles
  
  FindFrontFrwdNoAve<float> finder(data.data(), total);
  float pos = 0.0f; bool found = false;
  
  // Look for fronts in triangle wave
  bool ok = finder(true, 0, 0.0f, 0.0f, 5.0f, 1.5f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Triangle wave test failed");
  }
  
  std::cout << "✓ triangle wave fronts" << std::endl;
}

// Alternating pattern tests
void test_alternating_patterns() {
  std::vector<float> data(1000);
  
  // Create alternating high-low pattern
  for (size_t i = 0; i < data.size(); ++i) {
    data[i] = (i % 2 == 0) ? -1.0f : 1.0f;
  }
  
  FindFrontFrwdNoAve<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Should find fronts in alternating pattern
  bool ok = finder(true, 0, 0.0f, 0.0f, 3.0f, 1.5f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Alternating pattern test failed");
  }
  
  // Position should be very early (first transition)
  if (pos > 5.0f) {
    std::ostringstream oss;
    oss << "Alternating pattern position too late: " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ alternating patterns" << std::endl;
}

// Gradual slope tests
void test_gradual_slopes() {
  std::vector<float> data(500, -2.0f);
  
  // Create gradual slope from index 100 to 200
  for (size_t i = 100; i < 200; ++i) {
    data[i] = -2.0f + 4.0f * static_cast<float>(i - 100) / 100.0f;
  }
  for (size_t i = 200; i < data.size(); ++i) {
    data[i] = 2.0f;
  }
  
  FindFrontFrwdNoAve<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Try to detect the gradual slope
  bool ok = finder(true, +1, 0.0f, 0.0f, 10.0f, 1.0f, pos, found);

  if (!ok) {
    throw std::runtime_error("Gradual slope test execution failed");
  }

  // May or may not find gradual slopes depending on threshold
  // Just ensure it doesn't crash - gradual slopes may not trigger threshold
  if (found) {
    // If found, position should be somewhere reasonable
    if (pos < 50.0f || pos > 250.0f) {
      std::ostringstream oss;
      oss << "Gradual slope position out of range: " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ gradual slopes" << std::endl;
}

// Multiple threshold sizes on same data
void test_threshold_size_sensitivity() {
  const size_t total = 800;
  const size_t stepPos = 300;
  auto data = makeStep(total, stepPos, -1.0f, 1.0f);
  
  std::vector<float> thresholdSizes = {1.0f, 3.0f, 5.0f, 10.0f, 20.0f, 50.0f, 100.0f};
  std::vector<float> positions;
  
  for (float thresholdSize : thresholdSizes) {
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, thresholdSize, 0.8f, pos, found);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Threshold sensitivity test failed for size " << thresholdSize;
      throw std::runtime_error(oss.str());
    }
    
    positions.push_back(pos);
    
    // All positions should be reasonably close to the actual step
    if (std::abs(pos - static_cast<float>(stepPos)) > thresholdSize + 20.0f) {
      std::ostringstream oss;
      oss << "Position too far for threshold " << thresholdSize << ": " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ threshold size sensitivity" << std::endl;
}

// Random noise stress test
void test_random_noise_stress() {
  const size_t total = 2000;
  std::mt19937 gen(12345); // Fixed seed for reproducibility
  std::uniform_real_distribution<float> noise(-0.1f, 0.1f);
  
  // Create step with noise
  auto data = makeStep(total, 800, -1.0f, 1.0f);
  for (auto& val : data) {
    val += noise(gen);
  }
  
  FindFrontFrwdNoAve<float> finder(data.data(), total);
  float pos = 0.0f; bool found = false;
  
  // Should still find the step despite noise
  bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 1.5f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Random noise stress test failed");
  }
  
  // Position should be reasonably close to step
  if (std::abs(pos - 800.0f) > 50.0f) {
    std::ostringstream oss;
    oss << "Noisy step position inaccurate: expected ~800, got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ random noise stress" << std::endl;
}

// Very small data sizes edge cases
void test_minimal_data_sizes() {
  // Test with minimum possible data sizes
  for (size_t dataSize : {2, 3, 4, 5}) {
    std::vector<float> data(dataSize, 0.0f);
    data[dataSize - 1] = 2.0f; // Step at the end
    
    FindFrontFrwdNoAve<float> finder(data.data(), dataSize);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 1.0f, pos, found);
    
    if (!ok) {
      std::ostringstream oss;
      oss << "Minimal data size " << dataSize << " execution failed";
      throw std::runtime_error(oss.str());
    }
    
    // May or may not find depending on threshold - just ensure it doesn't crash
  }
  
  std::cout << "✓ minimal data sizes" << std::endl;
}

// Extreme value ranges
void test_extreme_value_ranges() {
  const size_t total = 300;
  const size_t stepPos = 150;
  
  // Very large values
  {
    auto data = makeStep(total, stepPos, -1e6f, 1e6f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 1e5f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Extreme large values test failed");
    }

    // Verify position accuracy for large values
    if (std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      std::ostringstream oss;
      oss << "Large values position inaccurate: expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  // Very small values
  {
    auto data = makeStep(total, stepPos, -1e-6f, 1e-6f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 1e-6f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Extreme small values test failed");
    }

    // Verify position accuracy for small values
    if (std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      std::ostringstream oss;
      oss << "Small values position inaccurate: expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  
  std::cout << "✓ extreme value ranges" << std::endl;
}

// Performance with different data patterns
void test_performance_patterns() {
  const size_t total = 100000;
  
  // Test performance with different patterns
  std::vector<std::pair<std::string, std::vector<float>>> testCases = {
    {"constant", std::vector<float>(total, 1.0f)},
    {"linear_ramp", {}},
    {"step", makeStep(total, total/2, -1.0f, 1.0f)},
    {"sine", makeSineWave(total, 1.0f, 10.0f)}
  };
  
  // Create linear ramp
  testCases[1].second.resize(total);
  for (size_t i = 0; i < total; ++i) {
    testCases[1].second[i] = -1.0f + 2.0f * static_cast<float>(i) / static_cast<float>(total);
  }
  
  for (const auto& testCase : testCases) {
    FindFrontFrwdNoAve<float> finder(testCase.second.data(), total);
    float pos = 0.0f; bool found = false;
    
    auto start = std::chrono::high_resolution_clock::now();
    bool ok = finder(true, +1, 0.0f, 0.0f, 10.0f, 0.5f, pos, found);
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    if (!ok) {
      std::ostringstream oss;
      oss << "Performance test failed for pattern: " << testCase.first;
      throw std::runtime_error(oss.str());
    }
    
    // Should complete in reasonable time
    if (duration.count() > 100) { // 100ms threshold
      std::ostringstream oss;
      oss << "Performance too slow for " << testCase.first << ": " << duration.count() << "ms";
      throw std::runtime_error(oss.str());
    }

    // Verify position accuracy for step pattern (has deterministic front position)
    if (testCase.first == "step" && found) {
      float expectedPos = static_cast<float>(total / 2);
      if (std::abs(pos - expectedPos) > 50.0f) {
        std::ostringstream oss;
        oss << "Step pattern position inaccurate: expected ~" << expectedPos << ", got " << pos;
        throw std::runtime_error(oss.str());
      }
    }
  }
  
  std::cout << "✓ performance with different patterns" << std::endl;
}

// Double precision validation
void test_double_precision_accuracy() {
  const size_t total = 1000;
  const size_t stepPos = 400;
  
  // Create double precision data
  std::vector<double> data(total, -1.5);
  for (size_t i = stepPos; i < total; ++i) {
    data[i] = 1.5;
  }
  
  FindFrontFrwdNoAve<double> finder(data.data(), total);
  double pos = 0.0; bool found = false;
  
  bool ok = finder(true, +1, 0.0, 0.0, 5.0, 1.0, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("Double precision test failed");
  }
  
  // Verify position accuracy
  if (std::abs(pos - static_cast<double>(stepPos)) > 10.0) {
    std::ostringstream oss;
    oss << "Double precision position inaccurate: expected ~" << stepPos << ", got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ double precision accuracy" << std::endl;
}

int main() {
  std::cout << "FindFrontFrwdNoAve Advanced Scenarios Test Suite" << std::endl;
  std::cout << "================================================" << std::endl;
  
  // Run all advanced tests
  RUN_TEST(test_sine_wave_fronts);
  RUN_TEST(test_triangle_wave_fronts);
  RUN_TEST(test_alternating_patterns);
  RUN_TEST(test_gradual_slopes);
  RUN_TEST(test_threshold_size_sensitivity);
  RUN_TEST(test_random_noise_stress);
  RUN_TEST(test_minimal_data_sizes);
  RUN_TEST(test_extreme_value_ranges);
  RUN_TEST(test_performance_patterns);
  RUN_TEST(test_double_precision_accuracy);
  
  // Print results summary
  std::cout << std::endl;
  std::cout << "===================================================" << std::endl;
  std::cout << "ADVANCED SCENARIOS TEST RESULTS" << std::endl;
  std::cout << "===================================================" << std::endl;
  
  int passed = 0, failed = 0;
  for (const auto& result : testResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }
  
  std::cout << std::endl;
  std::cout << "Total: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;
  
  return failed > 0 ? 1 : 0;
}
