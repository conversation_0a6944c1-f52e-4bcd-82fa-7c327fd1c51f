#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include "../find_front_frwd_no_ave.h"

using IQVideoProcessor::SignalProcessing::FindFrontFrwdNoAve;

// Test result tracking
struct TestResult {
  std::string name;
  bool passed;
  std::string error;
  double frontPos;
  bool frontFound;
};

std::vector<TestResult> testResults;

// Helper functions
static std::vector<float> makeStep(size_t total, size_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (size_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static void buildPadded(const std::vector<float>& effective, size_t leftPad, size_t rightPad, std::vector<float>& out) {
  out.resize(leftPad + effective.size() + rightPad);
  std::fill(out.begin(), out.begin() + leftPad, effective.front());
  std::copy(effective.begin(), effective.end(), out.begin() + leftPad);
  std::fill(out.begin() + leftPad + effective.size(), out.end(), effective.back());
}

// Test execution helper
#define RUN_TEST(test_func) do { \
  try { \
    test_func(); \
    testResults.push_back({#test_func, true, "", 0.0, false}); \
  } catch (const std::exception& e) { \
    testResults.push_back({#test_func, false, e.what(), 0.0, false}); \
  } \
} while(0)

void test_basic_no_averaging() {
  const size_t total = 256, leftPad = 64, rightPad = 64;
  auto effective = makeStep(total, 128, -0.5f, 0.5f);
  std::vector<float> padded; buildPadded(effective, leftPad, rightPad, padded);

  // Test basic functionality without averaging (equivalent to original aveSize=1)
  FindFrontFrwdNoAve<float> finder(padded.data() + leftPad, total);
  float pos = 0.0f; bool found = false;
  bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 0.2f, pos, found);
  
  if (!ok || !found) throw std::runtime_error("basic front detection failed");
  if (std::abs(pos - 128.0f) > 5.0f) throw std::runtime_error("front position inaccurate");
  
  std::cout << "✓ basic no averaging" << std::endl;
}

void test_boundary_conditions() {
  const size_t total = 64, pad = 16;
  
  // Test front at end - should work without averaging
  {
    const size_t stepPos = total - 8;
    auto effective = makeStep(total, stepPos, -0.5f, 0.5f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    FindFrontFrwdNoAve<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;
    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 0.2f, pos, found);
    if (!ok || !found) throw std::runtime_error("front at end not found");

    // Verify position accuracy - should be within ±5 samples of expected position
    if (std::abs(pos - static_cast<float>(stepPos)) > 5.0f) {
      std::ostringstream oss;
      oss << "front at end position inaccurate: expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ boundary conditions" << std::endl;
}

void test_threshold_variations() {
  const size_t total = 256, pad = 64;
  auto effective = makeStep(total, 128, -1.0f, 1.0f);
  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // Test various threshold sizes (no averaging)
  for (size_t thresholdSize : {1, 2, 3, 5, 7, 11, 17, 25, 33, 49, 65}) {
    if (thresholdSize >= total) continue;

    FindFrontFrwdNoAve<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, static_cast<float>(thresholdSize), 0.5f, pos, found);

    if (!ok || !found) {
      std::ostringstream oss;
      oss << "no-averaging failed with thresholdSize=" << thresholdSize;
      throw std::runtime_error(oss.str());
    }

    // Verify position accuracy (should be very precise without averaging)
    float expectedPos = 128.0f;
    float tolerance = static_cast<float>(thresholdSize) / 2.0f + 2.0f;
    if (std::abs(pos - expectedPos) > tolerance) {
      std::ostringstream oss;
      oss << "no-averaging position inaccurate with thresholdSize=" << thresholdSize
          << ": expected ~" << expectedPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ threshold variations" << std::endl;
}

void test_front_types() {
  const size_t total = 180, pad = 45;

  // Test rising fronts
  {
    const size_t stepPos = 90;
    auto effective = makeStep(total, stepPos, -0.8f, 0.8f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    for (size_t thresholdSize : {3, 7, 15, 31}) {
      FindFrontFrwdNoAve<float> finder(padded.data() + pad, total);
      float pos = 0.0f; bool found = false;

      bool ok = finder(true, +1, 0.0f, 0.0f, static_cast<float>(thresholdSize), 0.4f, pos, found);

      if (!ok || !found) {
        std::ostringstream oss;
        oss << "no-averaging rising front failed with thresholdSize=" << thresholdSize;
        throw std::runtime_error(oss.str());
      }

      // Verify position accuracy for rising front
      float tolerance = static_cast<float>(thresholdSize) / 2.0f + 3.0f;
      if (std::abs(pos - static_cast<float>(stepPos)) > tolerance) {
        std::ostringstream oss;
        oss << "rising front position inaccurate with thresholdSize=" << thresholdSize
            << ": expected ~" << stepPos << ", got " << pos;
        throw std::runtime_error(oss.str());
      }
    }
  }

  // Test falling fronts
  {
    const size_t stepPos = 90;
    auto effective = makeStep(total, stepPos, 0.8f, -0.8f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    for (size_t thresholdSize : {3, 7, 15, 31}) {
      FindFrontFrwdNoAve<float> finder(padded.data() + pad, total);
      float pos = 0.0f; bool found = false;

      bool ok = finder(true, -1, 0.0f, 0.0f, static_cast<float>(thresholdSize), 0.4f, pos, found);

      if (!ok || !found) {
        std::ostringstream oss;
        oss << "no-averaging falling front failed with thresholdSize=" << thresholdSize;
        throw std::runtime_error(oss.str());
      }

      // Verify position accuracy for falling front
      float tolerance = static_cast<float>(thresholdSize) / 2.0f + 3.0f;
      if (std::abs(pos - static_cast<float>(stepPos)) > tolerance) {
        std::ostringstream oss;
        oss << "falling front position inaccurate with thresholdSize=" << thresholdSize
            << ": expected ~" << stepPos << ", got " << pos;
        throw std::runtime_error(oss.str());
      }
    }
  }

  // Test bidirectional detection
  {
    const size_t stepPos = 90;
    auto effective = makeStep(total, stepPos, -0.6f, 0.6f);
    std::vector<float> padded; buildPadded(effective, pad, pad, padded);

    FindFrontFrwdNoAve<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, 0, 0.0f, 0.0f, 13.0f, 0.3f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("no-averaging bidirectional front detection failed");
    }

    // Verify position accuracy for bidirectional front
    float tolerance = 13.0f / 2.0f + 3.0f; // threshold/2 + margin
    if (std::abs(pos - static_cast<float>(stepPos)) > tolerance) {
      std::ostringstream oss;
      oss << "bidirectional front position inaccurate: expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ front types" << std::endl;
}

void test_edge_cases() {
  // Test single-element differences
  {
    std::vector<float> data = {0.0f, 0.0f, 1.0f, 1.0f}; // Single element change at index 2
    FindFrontFrwdNoAve<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 2.0f, 0.5f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("no-averaging single-element difference failed");
    }

    // Verify position accuracy - front should be detected near index 2
    if (pos < 1.0f || pos > 3.0f) {
      std::ostringstream oss;
      oss << "single-element difference position out of range: expected ~2, got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  // Test very small buffers
  {
    std::vector<float> data = {-1.0f, 1.0f}; // Minimal 2-element buffer
    FindFrontFrwdNoAve<float> finder(data.data(), data.size());
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 0.5f, pos, found);

    if (!ok || !found) {
      throw std::runtime_error("no-averaging minimal buffer failed");
    }
  }

  std::cout << "✓ edge cases" << std::endl;
}

void test_multiple_fronts() {
  const size_t total = 300, pad = 75;

  // Create signal with multiple fronts
  std::vector<float> effective(total, 0.0f);
  for (size_t i = 50; i < 70; ++i) effective[i] = 1.0f;   // First front at 50
  for (size_t i = 100; i < 120; ++i) effective[i] = 2.0f; // Second front at 100
  for (size_t i = 200; i < 220; ++i) effective[i] = 3.0f; // Third front at 200
  for (size_t i = 250; i < total; ++i) effective[i] = 4.0f; // Fourth front at 250

  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // Test with different threshold sizes - should detect first significant front
  for (size_t thresholdSize : {5, 11, 21, 41}) {
    FindFrontFrwdNoAve<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, static_cast<float>(thresholdSize), 0.2f, pos, found);

    if (!ok || !found) {
      std::ostringstream oss;
      oss << "no-averaging multiple fronts failed with thresholdSize=" << thresholdSize;
      throw std::runtime_error(oss.str());
    }

    // Should detect the first front around position 50
    if (pos < 20.0f || pos > 90.0f) {
      std::ostringstream oss;
      oss << "no-averaging multiple fronts position unexpected: " << pos
          << " with thresholdSize=" << thresholdSize;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ multiple fronts" << std::endl;
}

void test_rapid_changes() {
  const size_t total = 200, pad = 50;

  // Create signal with rapid alternating changes (would be smoothed by averaging)
  std::vector<float> effective(total);
  for (size_t i = 0; i < total; ++i) {
    if (i < 50) {
      effective[i] = 0.0f; // baseline
    } else if (i < 100) {
      effective[i] = (i % 2 == 0) ? 0.5f : -0.5f; // rapid alternation
    } else {
      effective[i] = 1.0f; // final level
    }
  }

  std::vector<float> padded; buildPadded(effective, pad, pad, padded);

  // No averaging should detect the first significant change
  for (size_t thresholdSize : {3, 7, 13, 25}) {
    FindFrontFrwdNoAve<float> finder(padded.data() + pad, total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, static_cast<float>(thresholdSize), 0.5f, pos, found);

    if (!ok || !found) {
      std::ostringstream oss;
      oss << "no-averaging rapid changes failed with thresholdSize=" << thresholdSize;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ rapid changes" << std::endl;
}

void test_double_precision_instantiation() {
  std::vector<double> data(100, 0.0);
  FindFrontFrwdNoAve<double> finderD(data.data(), data.size());
  // Just ensure template compiles and can be called with valid inputs
  double posD = 0.0; bool foundD = false;
  bool ok = finderD(true, +1, 0.0, 0.0, 33.0, 0.3, posD, foundD);
  (void)ok; (void)foundD;
  std::cout << "✓ double precision instantiation compiled" << std::endl;
}

int main() {
  std::cout << "FindFrontFrwdNoAve Test Suite" << std::endl;
  std::cout << "=============================" << std::endl;

  // Run all tests
  RUN_TEST(test_basic_no_averaging);
  RUN_TEST(test_boundary_conditions);
  RUN_TEST(test_threshold_variations);
  RUN_TEST(test_front_types);
  RUN_TEST(test_edge_cases);
  RUN_TEST(test_multiple_fronts);
  RUN_TEST(test_rapid_changes);
  RUN_TEST(test_double_precision_instantiation);

  // Print results summary
  std::cout << "\n" << std::string(50, '=') << std::endl;
  std::cout << "TEST RESULTS SUMMARY" << std::endl;
  std::cout << std::string(50, '=') << std::endl;

  size_t passed = 0, failed = 0;
  for (const auto& result : testResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << "\nTotal: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;

  return failed > 0 ? 1 : 0;
}
