#include <iostream>
#include <vector>
#include <stdexcept>
#include <cmath>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <random>
#include <chrono>
#include "../find_front_frwd_no_ave.h"

using IQVideoProcessor::SignalProcessing::FindFrontFrwdNoAve;

// Test result tracking
struct TestResult {
  std::string name;
  bool passed;
  std::string error;
  double frontPos;
  bool frontFound;
};

std::vector<TestResult> testResults;

// Helper functions
static std::vector<float> makeStep(size_t total, size_t stepIndex, float low, float high) {
  std::vector<float> v(total, low);
  for (size_t i = stepIndex; i < total; ++i) v[i] = high;
  return v;
}

static std::vector<float> makeRamp(size_t total, size_t startIdx, size_t endIdx, float startVal, float endVal) {
  std::vector<float> v(total, startVal);
  if (endIdx > startIdx) {
    float step = (endVal - startVal) / static_cast<float>(endIdx - startIdx);
    for (size_t i = startIdx; i <= endIdx && i < total; ++i) {
      v[i] = startVal + step * static_cast<float>(i - startIdx);
    }
    for (size_t i = endIdx + 1; i < total; ++i) v[i] = endVal;
  }
  return v;
}

static std::vector<float> makeNoisyStep(size_t total, size_t stepIndex, float low, float high, float noiseLevel, unsigned seed = 42) {
  auto v = makeStep(total, stepIndex, low, high);
  std::mt19937 gen(seed);
  std::uniform_real_distribution<float> noise(-noiseLevel, noiseLevel);
  for (auto& val : v) val += noise(gen);
  return v;
}

// Test execution helper
#define RUN_TEST(test_func) do { \
  try { \
    test_func(); \
    testResults.push_back({#test_func, true, "", 0.0, false}); \
  } catch (const std::exception& e) { \
    testResults.push_back({#test_func, false, e.what(), 0.0, false}); \
  } \
} while(0)

// Small data size tests (10-100 samples)
void test_small_data_sizes() {
  for (size_t dataSize : {10, 25, 50, 100}) {
    size_t stepPos = dataSize / 2;
    auto data = makeStep(dataSize, stepPos, -1.0f, 1.0f);
    
    FindFrontFrwdNoAve<float> finder(data.data(), dataSize);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 0.5f, pos, found);
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Small data test failed for size " << dataSize;
      throw std::runtime_error(oss.str());
    }
    
    // Verify position accuracy
    if (std::abs(pos - static_cast<float>(stepPos)) > 3.0f) {
      std::ostringstream oss;
      oss << "Position inaccurate for size " << dataSize << ": expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  std::cout << "✓ small data sizes (10-100 samples)" << std::endl;
}

// Medium data size tests (1K-10K samples)
void test_medium_data_sizes() {
  for (size_t dataSize : {1000, 2500, 5000, 10000}) {
    size_t stepPos = dataSize / 3;
    auto data = makeStep(dataSize, stepPos, -2.0f, 2.0f);
    
    FindFrontFrwdNoAve<float> finder(data.data(), dataSize);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Medium data test failed for size " << dataSize;
      throw std::runtime_error(oss.str());
    }
    
    // Verify position accuracy
    if (std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      std::ostringstream oss;
      oss << "Position inaccurate for size " << dataSize << ": expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }
  std::cout << "✓ medium data sizes (1K-10K samples)" << std::endl;
}

// Large data size tests (100K+ samples)
void test_large_data_sizes() {
  for (size_t dataSize : {100000, 250000, 500000}) {
    size_t stepPos = dataSize / 4;
    auto data = makeStep(dataSize, stepPos, -3.0f, 3.0f);
    
    FindFrontFrwdNoAve<float> finder(data.data(), dataSize);
    float pos = 0.0f; bool found = false;
    
    auto start = std::chrono::high_resolution_clock::now();
    bool ok = finder(true, +1, 0.0f, 0.0f, 10.0f, 2.0f, pos, found);
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Large data test failed for size " << dataSize;
      throw std::runtime_error(oss.str());
    }
    
    // Verify position accuracy
    if (std::abs(pos - static_cast<float>(stepPos)) > 20.0f) {
      std::ostringstream oss;
      oss << "Position inaccurate for size " << dataSize << ": expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
    
    // Performance check - should complete in reasonable time
    if (duration.count() > 1000) { // More than 1 second is too slow
      std::ostringstream oss;
      oss << "Performance too slow for size " << dataSize << ": " << duration.count() << "ms";
      throw std::runtime_error(oss.str());
    }
  }
  std::cout << "✓ large data sizes (100K+ samples)" << std::endl;
}

// Different front types comprehensive test
void test_comprehensive_front_types() {
  const size_t total = 1000;
  const size_t stepPos = 400;
  
  // Rising front test
  {
    auto data = makeStep(total, stepPos, -2.0f, 2.0f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, 1.5f, pos, found);
    if (!ok || !found || std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      throw std::runtime_error("Rising front detection failed");
    }
  }
  
  // Falling front test
  {
    auto data = makeStep(total, stepPos, 2.0f, -2.0f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, -1, 0.0f, 0.0f, 5.0f, 1.5f, pos, found);
    if (!ok || !found || std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      throw std::runtime_error("Falling front detection failed");
    }
  }
  
  // Bidirectional test with rising
  {
    auto data = makeStep(total, stepPos, -1.5f, 1.5f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, 0, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok || !found || std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      throw std::runtime_error("Bidirectional (rising) front detection failed");
    }
  }
  
  // Bidirectional test with falling
  {
    auto data = makeStep(total, stepPos, 1.5f, -1.5f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, 0, 0.0f, 0.0f, 5.0f, 1.0f, pos, found);
    if (!ok || !found || std::abs(pos - static_cast<float>(stepPos)) > 10.0f) {
      throw std::runtime_error("Bidirectional (falling) front detection failed");
    }
  }
  
  std::cout << "✓ comprehensive front types" << std::endl;
}

// Edge cases: boundary conditions
void test_boundary_edge_cases() {
  // Front at very beginning
  {
    auto data = makeStep(100, 1, -1.0f, 1.0f);
    FindFrontFrwdNoAve<float> finder(data.data(), 100);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 0.8f, pos, found);
    if (!ok || !found || pos > 5.0f) {
      throw std::runtime_error("Boundary case: front at beginning failed");
    }
  }
  
  // Front at very end
  {
    auto data = makeStep(100, 95, -1.0f, 1.0f);
    FindFrontFrwdNoAve<float> finder(data.data(), 100);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 0.8f, pos, found);
    if (!ok || !found || std::abs(pos - 95.0f) > 5.0f) {
      throw std::runtime_error("Boundary case: front at end failed");
    }
  }
  
  std::cout << "✓ boundary edge cases" << std::endl;
}

// Edge cases: identical values
void test_identical_values_edge_cases() {
  // All identical values - should not find front
  {
    std::vector<float> data(100, 1.5f);
    FindFrontFrwdNoAve<float> finder(data.data(), 100);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 0.5f, pos, found);
    if (!ok || found) {
      throw std::runtime_error("Identical values should not detect front");
    }
  }
  
  // Very small differences
  {
    std::vector<float> data(100, 1.0f);
    data[50] = 1.0f + 1e-5f; // Tiny step
    FindFrontFrwdNoAve<float> finder(data.data(), 100);
    float pos = 0.0f; bool found = false;
    
    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 5e-6f, pos, found);
    if (!ok) {
      throw std::runtime_error("Tiny differences test execution failed");
    }
    // May or may not find depending on precision - just ensure it doesn't crash
  }
  
  std::cout << "✓ identical values edge cases" << std::endl;
}

// Precision limits testing
void test_precision_limits() {
  const float epsilon = std::numeric_limits<float>::epsilon();

  // Test with values near floating point precision limits
  {
    std::vector<float> data(100, 1.0f);
    data[50] = 1.0f + 10 * epsilon;
    FindFrontFrwdNoAve<float> finder(data.data(), 100);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 5 * epsilon, pos, found);
    if (!ok) {
      throw std::runtime_error("Precision limits test execution failed");
    }
  }

  // Test with very large values
  {
    std::vector<float> data(100, 1e6f);
    data[50] = 1e6f + 1000.0f;
    FindFrontFrwdNoAve<float> finder(data.data(), 100);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 500.0f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Large values test failed");
    }
  }

  std::cout << "✓ precision limits" << std::endl;
}

// Different threshold configurations
void test_threshold_configurations() {
  const size_t total = 500;
  const size_t stepPos = 200;
  auto data = makeStep(total, stepPos, -2.0f, 2.0f);

  // Test various threshold sizes
  for (float thresholdSize : {1.0f, 2.0f, 5.0f, 10.0f, 20.0f, 50.0f}) {
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, thresholdSize, 1.0f, pos, found);
    if (!ok || !found) {
      std::ostringstream oss;
      oss << "Threshold size " << thresholdSize << " failed";
      throw std::runtime_error(oss.str());
    }

    // Position should be reasonably accurate
    if (std::abs(pos - static_cast<float>(stepPos)) > thresholdSize + 10.0f) {
      std::ostringstream oss;
      oss << "Position inaccurate for threshold " << thresholdSize << ": expected ~" << stepPos << ", got " << pos;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ threshold configurations" << std::endl;
}

// Different delta values
void test_delta_values() {
  const size_t total = 300;
  const size_t stepPos = 150;

  // Test with different step amplitudes and corresponding deltas
  std::vector<std::pair<float, float>> testCases = {
    {0.1f, 0.05f},   // Small step, small delta
    {0.5f, 0.2f},    // Medium step, medium delta
    {2.0f, 1.0f},    // Large step, large delta
    {5.0f, 3.0f},    // Very large step, large delta
    {0.01f, 0.005f}  // Tiny step, tiny delta
  };

  for (auto& testCase : testCases) {
    float amplitude = testCase.first;
    float delta = testCase.second;

    auto data = makeStep(total, stepPos, -amplitude, amplitude);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, delta, pos, found);
    if (!ok) {
      std::ostringstream oss;
      oss << "Delta test execution failed for amplitude " << amplitude << ", delta " << delta;
      throw std::runtime_error(oss.str());
    }

    // For larger deltas, we should find the front
    if (delta < amplitude && !found) {
      std::ostringstream oss;
      oss << "Should have found front for amplitude " << amplitude << ", delta " << delta;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ delta values" << std::endl;
}

// Performance stress tests
void test_performance_stress() {
  // Test with unusual parameter combinations that might stress the algorithm
  const size_t total = 50000;
  const size_t stepPos = 25000;
  auto data = makeStep(total, stepPos, -1.0f, 1.0f);

  // Very large threshold size
  {
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    auto start = std::chrono::high_resolution_clock::now();
    bool ok = finder(true, +1, 0.0f, 0.0f, 1000.0f, 0.5f, pos, found);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    if (!ok || !found) {
      throw std::runtime_error("Large threshold stress test failed");
    }

    if (duration.count() > 500) { // Should complete in reasonable time
      std::ostringstream oss;
      oss << "Large threshold too slow: " << duration.count() << "ms";
      throw std::runtime_error(oss.str());
    }
  }

  // Very small threshold with large data
  {
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    auto start = std::chrono::high_resolution_clock::now();
    bool ok = finder(true, +1, 0.0f, 0.0f, 1.0f, 0.5f, pos, found);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    if (!ok || !found) {
      throw std::runtime_error("Small threshold stress test failed");
    }

    if (duration.count() > 200) {
      std::ostringstream oss;
      oss << "Small threshold too slow: " << duration.count() << "ms";
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ performance stress tests" << std::endl;
}

// Noisy data tests
void test_noisy_data() {
  const size_t total = 1000;
  const size_t stepPos = 400;

  // Test with different noise levels
  for (float noiseLevel : {0.01f, 0.05f, 0.1f, 0.2f}) {
    auto data = makeNoisyStep(total, stepPos, -1.0f, 1.0f, noiseLevel);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    // Use larger delta to handle noise
    float delta = 0.5f + noiseLevel * 2;
    bool ok = finder(true, +1, 0.0f, 0.0f, 5.0f, delta, pos, found);

    if (!ok) {
      std::ostringstream oss;
      oss << "Noisy data test execution failed for noise level " << noiseLevel;
      throw std::runtime_error(oss.str());
    }

    // Should still find front with appropriate delta
    if (!found && noiseLevel < 0.15f) {
      std::ostringstream oss;
      oss << "Should have found front with noise level " << noiseLevel;
      throw std::runtime_error(oss.str());
    }
  }

  std::cout << "✓ noisy data" << std::endl;
}

// Ramp vs step detection
void test_ramp_vs_step() {
  const size_t total = 500;

  // Sharp step - should be detected easily
  {
    auto data = makeStep(total, 200, -1.0f, 1.0f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 0.5f, pos, found);
    if (!ok || !found) {
      throw std::runtime_error("Sharp step detection failed");
    }
  }

  // Gradual ramp - may or may not be detected depending on threshold
  {
    auto data = makeRamp(total, 180, 220, -1.0f, 1.0f);
    FindFrontFrwdNoAve<float> finder(data.data(), total);
    float pos = 0.0f; bool found = false;

    bool ok = finder(true, +1, 0.0f, 0.0f, 3.0f, 0.5f, pos, found);
    if (!ok) {
      throw std::runtime_error("Ramp test execution failed");
    }
    // May or may not find - just ensure it doesn't crash
  }

  std::cout << "✓ ramp vs step detection" << std::endl;
}

// Multiple fronts in sequence
void test_multiple_fronts_sequence() {
  std::vector<float> data(1000, 0.0f);

  // Create multiple steps
  for (size_t i = 100; i < 200; ++i) data[i] = 1.0f;
  for (size_t i = 300; i < 400; ++i) data[i] = -1.0f;
  for (size_t i = 500; i < 600; ++i) data[i] = 2.0f;
  for (size_t i = 700; i < 800; ++i) data[i] = -2.0f;

  FindFrontFrwdNoAve<float> finder(data.data(), 1000);
  float pos = 0.0f; bool found = false;

  // Should find the first front
  bool ok = finder(true, 0, 0.0f, 0.0f, 5.0f, 0.5f, pos, found);
  if (!ok || !found) {
    throw std::runtime_error("Multiple fronts sequence test failed");
  }

  // Position should be near first front
  if (pos > 150.0f) {
    std::ostringstream oss;
    oss << "First front position incorrect: got " << pos;
    throw std::runtime_error(oss.str());
  }

  std::cout << "✓ multiple fronts sequence" << std::endl;
}

int main() {
  std::cout << "FindFrontFrwdNoAve Comprehensive Test Suite" << std::endl;
  std::cout << "===========================================" << std::endl;

  // Run all comprehensive tests
  RUN_TEST(test_small_data_sizes);
  RUN_TEST(test_medium_data_sizes);
  RUN_TEST(test_large_data_sizes);
  RUN_TEST(test_comprehensive_front_types);
  RUN_TEST(test_boundary_edge_cases);
  RUN_TEST(test_identical_values_edge_cases);
  RUN_TEST(test_precision_limits);
  RUN_TEST(test_threshold_configurations);
  RUN_TEST(test_delta_values);
  RUN_TEST(test_performance_stress);
  RUN_TEST(test_noisy_data);
  RUN_TEST(test_ramp_vs_step);
  RUN_TEST(test_multiple_fronts_sequence);

  // Print results summary
  std::cout << std::endl;
  std::cout << "===================================================" << std::endl;
  std::cout << "COMPREHENSIVE TEST RESULTS" << std::endl;
  std::cout << "===================================================" << std::endl;

  int passed = 0, failed = 0;
  for (const auto& result : testResults) {
    if (result.passed) {
      std::cout << "✓ " << result.name << std::endl;
      ++passed;
    } else {
      std::cout << "❌ " << result.name << ": " << result.error << std::endl;
      ++failed;
    }
  }

  std::cout << std::endl;
  std::cout << "Total: " << (passed + failed) << " tests" << std::endl;
  std::cout << "Passed: " << passed << std::endl;
  std::cout << "Failed: " << failed << std::endl;

  return failed > 0 ? 1 : 0;
}
