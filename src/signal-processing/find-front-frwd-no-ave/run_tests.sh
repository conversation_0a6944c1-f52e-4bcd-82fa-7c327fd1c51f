#!/bin/bash
set -e

CXX=${CXX:-g++}
CXXFLAGS="-std=c++17 -Wall -Wextra -O2 -g -pedantic"
INCLUDES="-I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline"

SRC_DIR=$(cd "$(dirname "$0")" && pwd)
cd "$SRC_DIR"

echo "FindFrontFrwdNoAve Test Runner"
echo "=============================="

$CXX $CXXFLAGS $INCLUDES find_front_frwd_no_ave.cpp tests/test_find_front_frwd_no_ave.cpp -o find_front_frwd_no_ave_tests
$CXX $CXXFLAGS $INCLUDES find_front_frwd_no_ave.cpp tests/test_front_detection_no_ave.cpp -o front_detection_no_ave_tests
$CXX $CXXFLAGS $INCLUDES find_front_frwd_no_ave.cpp tests/test_comprehensive_coverage.cpp -o comprehensive_coverage_tests
$CXX $CXXFLAGS $INCLUDES find_front_frwd_no_ave.cpp tests/test_advanced_scenarios.cpp -o advanced_scenarios_tests

echo "Running general algorithm tests..."
./find_front_frwd_no_ave_tests

echo ""
echo "Running front detection tests..."
./front_detection_no_ave_tests

echo ""
echo "Running comprehensive coverage tests..."
./comprehensive_coverage_tests

echo ""
echo "Running advanced scenarios tests..."
./advanced_scenarios_tests

echo "All FindFrontFrwdNoAve tests completed."
