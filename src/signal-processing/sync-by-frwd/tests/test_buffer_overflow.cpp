#include "../sync_by_frwd.h"
#include <vector>
#include <iostream>
#include <sstream>
#include <cmath>

using namespace IQVideoProcessor::SignalProcessing;

/**
 * Test to expose the buffer overflow bug in findPreciseValueLocation
 */

void test_buffer_overflow_detection() {
  // Create a minimal buffer that would expose buffer overflow
  // The bug is in findPreciseValueLocation where it accesses data[i+1] 
  // but loops to i < thresholdSize instead of i < thresholdSize-1
  
  std::vector<float> data = {0.0f, 1.0f, 2.0f}; // Only 3 elements
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Use thresholdSize = 3, which means the loop will access data[3] (out of bounds)
  // when i = 2 in the findPreciseValueLocation function
  bool ok = finder(true, +1, 0.5f, 0.0f, 0.0f, 3.0f, 0.8f, pos, found);
  
  // This test should either crash (exposing the buffer overflow) 
  // or we need to fix the buffer overflow before this test passes safely
  std::cout << "Buffer overflow test completed - ok: " << ok << ", found: " << found << ", pos: " << pos << std::endl;
}

void test_minimal_threshold_size() {
  // Test with thresholdSize = 1 (minimum possible)
  std::vector<float> data = {0.0f, 1.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // With thresholdSize = 1, the loop should access data[0] and data[1]
  // This should be safe if the buffer overflow is fixed
  bool ok = finder(true, +1, 0.5f, 0.0f, 0.0f, 1.0f, 0.8f, pos, found);
  
  if (!ok) {
    throw std::runtime_error("minimal threshold size test failed");
  }
  
  std::cout << "✓ minimal threshold size test" << std::endl;
}

void test_exact_threshold_boundary() {
  // Test where the crossing is exactly at the boundary of what should be accessible
  std::vector<float> data = {0.0f, 0.5f, 1.0f, 1.5f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // thresholdSize = 3 means we should only access indices 0,1,2 and 1,2,3 as pairs
  // But the current bug would try to access data[3] and data[4] when i=3
  bool ok = finder(true, +1, 0.75f, 0.0f, 0.0f, 3.0f, 0.3f, pos, found);
  
  std::cout << "Exact threshold boundary test - ok: " << ok << ", found: " << found << ", pos: " << pos << std::endl;
}

int main() {
  std::cout << "Buffer Overflow Detection Test Suite" << std::endl;
  std::cout << "====================================" << std::endl;
  
  try {
    test_buffer_overflow_detection();
    test_minimal_threshold_size();
    test_exact_threshold_boundary();
    
    std::cout << std::endl;
    std::cout << "Buffer overflow tests completed (but may have undefined behavior)" << std::endl;
    
    return 0;
  } catch (const std::exception& e) {
    std::cerr << "❌ " << e.what() << std::endl;
    return 1;
  }
}
