#include "../sync_by_frwd.h"
#include <vector>
#include <iostream>
#include <sstream>
#include <cmath>
#include <algorithm>

using namespace IQVideoProcessor::SignalProcessing;

/**
 * Regression tests for the findPreciseValueLocation bug fix
 * 
 * These tests specifically target the bug where findPreciseValueLocation
 * was called with the entire processing buffer instead of the relevant
 * threshold window where the crossing was detected.
 */

void test_threshold_window_boundary_crossing() {
  // Test case where the crossing occurs exactly at the boundary of the threshold window
  std::vector<float> data = {0.0f, 0.0f, 0.0f, 0.5f, 1.0f, 1.0f, 1.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Search for value 0.75 with threshold size 3
  // This should find the crossing between indices 3 and 4 (0.5 and 1.0)
  bool ok = finder(true, +1, 0.75f, 0.0f, 0.0f, 3.0f, 0.3f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("threshold window boundary crossing test failed");
  }
  
  // Expected position should be 3.5 (halfway between indices 3 and 4)
  if (std::abs(pos - 3.5f) > 0.1f) {
    std::ostringstream oss;
    oss << "boundary crossing position incorrect: expected ~3.5, got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ threshold window boundary crossing" << std::endl;
}

void test_multiple_crossings_in_buffer() {
  // Test case with multiple crossings to ensure we find the correct one
  // This would have failed with the original bug because findPreciseValueLocation
  // would find the first crossing, not the one detected by the main algorithm
  std::vector<float> data = {-1.0f, 0.0f, 1.0f, 0.0f, 1.0f, 2.0f, 1.0f, 2.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Search for value 0.5 with threshold size 4
  // The algorithm should detect the crossing that meets the threshold criteria,
  // not just the first crossing in the buffer
  bool ok = finder(true, +1, 0.5f, 0.0f, 0.0f, 4.0f, 0.8f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("multiple crossings test failed");
  }
  
  // The exact position depends on which crossing is detected first by the threshold algorithm
  // but it should be a valid interpolated position
  if (pos < 0.0f || pos >= static_cast<float>(data.size())) {
    std::ostringstream oss;
    oss << "multiple crossings position out of range: got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ multiple crossings in buffer" << std::endl;
}

void test_crossing_at_threshold_start() {
  // Test case where the crossing occurs right at the start of the threshold window
  std::vector<float> data = {0.0f, 1.0f, 1.0f, 1.0f, 1.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Search for value 0.5 with threshold size 2
  bool ok = finder(true, +1, 0.5f, 0.0f, 0.0f, 2.0f, 0.8f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("crossing at threshold start test failed");
  }
  
  // Expected position should be 0.5 (halfway between indices 0 and 1)
  if (std::abs(pos - 0.5f) > 0.1f) {
    std::ostringstream oss;
    oss << "threshold start crossing position incorrect: expected ~0.5, got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ crossing at threshold start" << std::endl;
}

void test_crossing_at_threshold_end() {
  // Test case where the crossing occurs at the end of the threshold window
  std::vector<float> data = {0.0f, 0.0f, 0.0f, 0.0f, 0.5f, 1.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Search for value 0.75 with threshold size 3
  bool ok = finder(true, +1, 0.75f, 0.0f, 0.0f, 3.0f, 0.3f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("crossing at threshold end test failed");
  }
  
  // Expected position should be 4.5 (halfway between indices 4 and 5)
  if (std::abs(pos - 4.5f) > 0.1f) {
    std::ostringstream oss;
    oss << "threshold end crossing position incorrect: expected ~4.5, got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ crossing at threshold end" << std::endl;
}

void test_window_size_edge_cases() {
  // Test with minimum threshold size
  std::vector<float> data = {0.0f, 1.0f, 2.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Search with threshold size 1 (minimum)
  bool ok = finder(true, +1, 0.5f, 0.0f, 0.0f, 1.0f, 0.8f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("minimum threshold size test failed");
  }
  
  // Should find crossing between indices 0 and 1
  if (std::abs(pos - 0.5f) > 0.1f) {
    std::ostringstream oss;
    oss << "minimum threshold crossing position incorrect: expected ~0.5, got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ window size edge cases" << std::endl;
}

void test_precise_interpolation_accuracy() {
  // Test that interpolation is mathematically correct
  std::vector<float> data = {1.0f, 1.0f, 1.0f, 2.0f, 5.0f, 5.0f};
  
  SyncByFrwd<float> finder(data.data(), data.size());
  float pos = 0.0f; bool found = false;
  
  // Search for value 3.0 (should be 1/3 of the way from 2.0 to 5.0)
  bool ok = finder(true, +1, 3.0f, 0.0f, 0.0f, 3.0f, 1.5f, pos, found);
  
  if (!ok || !found) {
    throw std::runtime_error("precise interpolation accuracy test failed");
  }
  
  // Expected position should be 3 + 1/3 = 3.333...
  float expected = 3.0f + (3.0f - 2.0f) / (5.0f - 2.0f);
  if (std::abs(pos - expected) > 0.01f) {
    std::ostringstream oss;
    oss << "interpolation accuracy incorrect: expected " << expected << ", got " << pos;
    throw std::runtime_error(oss.str());
  }
  
  std::cout << "✓ precise interpolation accuracy" << std::endl;
}

int main() {
  std::cout << "SyncByFrwd Bug Regression Test Suite" << std::endl;
  std::cout << "====================================" << std::endl;
  
  try {
    test_threshold_window_boundary_crossing();
    test_multiple_crossings_in_buffer();
    test_crossing_at_threshold_start();
    test_crossing_at_threshold_end();
    test_window_size_edge_cases();
    test_precise_interpolation_accuracy();
    
    std::cout << std::endl;
    std::cout << "===================================================" << std::endl;
    std::cout << "BUG REGRESSION TEST RESULTS" << std::endl;
    std::cout << "===================================================" << std::endl;
    std::cout << "✓ test_threshold_window_boundary_crossing" << std::endl;
    std::cout << "✓ test_multiple_crossings_in_buffer" << std::endl;
    std::cout << "✓ test_crossing_at_threshold_start" << std::endl;
    std::cout << "✓ test_crossing_at_threshold_end" << std::endl;
    std::cout << "✓ test_window_size_edge_cases" << std::endl;
    std::cout << "✓ test_precise_interpolation_accuracy" << std::endl;
    std::cout << std::endl;
    std::cout << "Total: 6 tests" << std::endl;
    std::cout << "Passed: 6" << std::endl;
    std::cout << "Failed: 0" << std::endl;
    
    return 0;
  } catch (const std::exception& e) {
    std::cerr << "❌ " << e.what() << std::endl;
    return 1;
  }
}
