# Makefile for SyncByFrwd module tests

CXX ?= g++
CXXFLAGS ?= -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline

TEST_SOURCES = sync_by_frwd.cpp tests/test_sync_by_frwd.cpp
TEST_EXECUTABLE = sync_by_frwd_tests

SYNC_DETECTION_SOURCES = sync_by_frwd.cpp tests/test_sync_detection.cpp
SYNC_DETECTION_EXECUTABLE = sync_detection_tests

EDGE_CASES_SOURCES = sync_by_frwd.cpp tests/test_sync_edge_cases.cpp
EDGE_CASES_EXECUTABLE = sync_edge_cases_tests

all: test

$(TEST_EXECUTABLE): $(TEST_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

$(SYNC_DETECTION_EXECUTABLE): $(SYNC_DETECTION_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

$(EDGE_CASES_EXECUTABLE): $(EDGE_CASES_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $^

build: $(TEST_EXECUTABLE) $(SYNC_DETECTION_EXECUTABLE) $(EDGE_CASES_EXECUTABLE)

test: $(TEST_EXECUTABLE) $(SYNC_DETECTION_EXECUTABLE) $(EDGE_CASES_EXECUTABLE)
	./$(TEST_EXECUTABLE)
	./$(SYNC_DETECTION_EXECUTABLE)
	./$(EDGE_CASES_EXECUTABLE)

clean:
	rm -f $(TEST_EXECUTABLE) $(SYNC_DETECTION_EXECUTABLE) $(EDGE_CASES_EXECUTABLE) tests/*.o *.o

.PHONY: all build test clean
