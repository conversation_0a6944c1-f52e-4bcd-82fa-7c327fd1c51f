#pragma once

#include "../iiq-stream/iiq_stream.h"
#include "./pipeline/pipeline.h"
#include <memory>
#include <thread>

namespace IQVideoProcessor {

class VideoProcessor {
public:
  explicit VideoProcessor(std::unique_ptr<IIQStream> stream);
  ~VideoProcessor();

  bool initialize();
  void shutdown();
  void start(); // New method for starting worker thread

private:
  std::unique_ptr<IIQStream> stream_;
  SampleRateType sampleRate_ = 0; // Sample rate of the video stream

  size_t maxVideoLineSamples_ = 0; // Maximum samples per video line
  bool initialized_;

  // Pipeline components (pointer-based storage)
  std::unique_ptr<Pipeline::IQAcquisitionNode> acquisitionNode_;
  std::unique_ptr<Pipeline::IQAcquisitionBridge> acquisitionBridge_;
  std::unique_ptr<Pipeline::IQDemodulationNode> demodNode_;
  std::unique_ptr<Pipeline::IQDemodulationLink> demodPassthrough_;
  std::unique_ptr<Pipeline::LineDetectionNode> lineDetectionNode_;

  // Worker thread
  std::unique_ptr<std::thread> workerThread_;

  void connectPipelineNodes();
};

} // namespace IQVideoProcessor
