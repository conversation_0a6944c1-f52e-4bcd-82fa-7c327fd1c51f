#include <algorithm>
#include <iostream>
#include "./video_processor.h"
#include "./video_processor_configs.h"

class IIQStream;
namespace IQVideoProcessor {

VideoProcessor::VideoProcessor(std::unique_ptr<IIQStream> stream) : stream_(std::move(stream)), initialized_(false) {

}

VideoProcessor::~VideoProcessor() {
  shutdown();
}

bool VideoProcessor::initialize() {
  if (initialized_) {
    return true;
  }

  if (!stream_) return false;

  sampleRate_ = stream_->sampleRate();
  if (sampleRate_ == 0) return false;

  // Calculating the maximum samples per video line, required to pre-allocate buffers and other computations
  maxVideoLineSamples_ = static_cast<size_t>(static_cast<TFloat>(sampleRate_) / MIN_LINE_RATE_HZ);
  if (maxVideoLineSamples_ < MIN_SAMPLES_PER_VIDEO_LINE) return false;

  // Calculate the desired amount of processable samples and overlap per window
  auto effectiveSamples = static_cast<size_t>(static_cast<TFloat>(maxVideoLineSamples_) * LINES_PER_CHUNK); // ~100 lines
  auto leftOverlap = maxVideoLineSamples_ * 8; // 8 lines overlap on each side, to ensure we catch non-standard VSync pulses

  // Create pipeline components using pointer-based storage
  // Note: stream_ is moved to acquisitionNode_, so it becomes nullptr after this
  acquisitionNode_ = std::make_unique<Pipeline::IQAcquisitionNode>(std::move(stream_), IQ_STREAM_READ_SIZE, effectiveSamples, leftOverlap);
  acquisitionBridge_ = std::make_unique<Pipeline::IQAcquisitionBridge>(100);
  demodNode_ = std::make_unique<Pipeline::IQDemodulationNode>();
  demodPassthrough_ = std::make_unique<Pipeline::IQDemodulationLink>();
  lineDetectionNode_ = std::make_unique<Pipeline::LineDetectionNode>(sampleRate_);

  connectPipelineNodes();

  initialized_ = true;
  return true;
}

void VideoProcessor::connectPipelineNodes() {
  // Acquisition node reads from the stream
  acquisitionNode_->sendDataTo(acquisitionBridge_.get());

  demodNode_->receiveDataFrom(acquisitionBridge_.get());
  demodNode_->sendDataTo(demodPassthrough_.get());

  lineDetectionNode_->receiveDataFrom(demodPassthrough_.get());
}

void VideoProcessor::shutdown() {
  if (!initialized_) {
    return;
  }

  // Stop worker thread if running
  if (workerThread_ && workerThread_->joinable()) {
    workerThread_->join();
  }
  workerThread_.reset();

  // Clean up pipeline components
  acquisitionNode_.reset();
  acquisitionBridge_.reset();
  demodNode_.reset();
  demodPassthrough_.reset();
  lineDetectionNode_.reset();

  initialized_ = false;
}

void VideoProcessor::start() {
  if (!initialized_) return;
  if (workerThread_) return;

  // Create worker thread with empty lambda function (placeholder for future processing logic)
  workerThread_ = std::make_unique<std::thread>([this]() {
    while (true) {
      if (!acquisitionBridge_->tick()) break;
    }
  });

  while (true) {
    if (!acquisitionNode_->tick()) break;
  }

  // Wait for the worker thread to complete (join)
  if (workerThread_->joinable()) {
    workerThread_->join();
  }
  std::cout << "a after join = " << std::endl; // should print 99
}

} // namespace IQVideoProcessor
