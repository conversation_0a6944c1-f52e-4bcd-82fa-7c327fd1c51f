# Makefile for VideoProcessor Tests
# BladeRF Video Decoding Project - VideoProcessor Module

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I. -I./tests -I.. -I../.. -I../../chunk-processor -I../../iiq-stream -I../../stream-pipeline

# Source directories
TEST_DIR = tests

# Source files for single test executable
TEST_SOURCES = $(TEST_DIR)/test_runner.cpp \
               $(TEST_DIR)/test_video_processor.cpp \
               $(TEST_DIR)/test_iq_acquisition_node.cpp \
               $(TEST_DIR)/test_video_standard_detector.cpp

# Implementation files needed for testing
IMPL_SOURCES = video_processor.cpp \
               pipeline/iq_acquisition_node.cpp \
               pipeline/iq_demodulation_node.cpp \
               pipeline/line-detection-node/line_detection_node.cpp \
               pipeline/line-detection-node/partials/video_standard_detector.cpp \
               pipeline/line-detection-node/partials/pulse_type_detector.cpp \
               pipeline/line-detection-node/partials/video-sync-detector.cpp \
               pipeline/line-detection-node/partials/segment-ave-filter.cpp \
               helpers/helpers.cpp \
               ../signal-processing/find-front-frwd/find_front_frwd.cpp \
               ../signal-processing/sync-by-frwd/sync_by_frwd.cpp

# Object files
TEST_OBJECTS = $(TEST_SOURCES:.cpp=.o)
IMPL_OBJECTS = $(IMPL_SOURCES:.cpp=.o)

# Single test executable
TEST_EXECUTABLE = video_processor_tests

# Header files for dependency tracking
HEADERS = video_processor.h \
          pipeline/iq_acquisition_node.h \
          pipeline/iq_acquisition_node_types.h \
          pipeline/iq_demodulation_node.h \
          pipeline/iq_demodulation_node_types.h \
          pipeline/line-detection-node/line_detection_node.h \
          pipeline/line-detection-node/partials/video_standard_detector.h \
          pipeline/line-detection-node/partials/pulse_type_detector.h \
          pipeline/line-detection-node/partials/video-sync-detector.h \
          pipeline/line-detection-node/partials/segment-ave-filter.h \
          types.h

.PHONY: all clean build test verify perf validate-header help

# Default target - build and run tests
all: test

# Build the single test executable
$(TEST_EXECUTABLE): $(TEST_OBJECTS) $(IMPL_OBJECTS)
	$(CXX) $(CXXFLAGS) -o $@ $^

# Object file compilation for tests
$(TEST_DIR)/%.o: $(TEST_DIR)/%.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Object file compilation for implementation
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Build and run tests in one command
test: $(TEST_EXECUTABLE)
	@echo "Running all VideoProcessor tests..."
	@echo "=================================="
	./$(TEST_EXECUTABLE)

# Build only (without running)
build: $(TEST_EXECUTABLE)
	@echo "VideoProcessor test executable built: $(TEST_EXECUTABLE)"

# Quick verification (build and run with limited output)
verify: $(TEST_EXECUTABLE)
	@echo "Quick verification of VideoProcessor..."
	@echo "======================================"
	@./$(TEST_EXECUTABLE) | grep -E "(PASS|FAIL|===|🎉|❌)" | head -15
	@echo "======================================"
	@echo "Run 'make test' for full output"

# Performance test (run only performance benchmarks)
perf: $(TEST_EXECUTABLE)
	@echo "Running VideoProcessor performance tests..."
	@echo "==========================================="
	@./$(TEST_EXECUTABLE) | grep -A 20 "PERFORMANCE TESTS"

# Validate header-only dependencies
validate-header:
	@echo "Validating VideoProcessor headers..."
	@echo "===================================="
	@for header in $(HEADERS); do \
		echo "Checking $$header..."; \
		$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only $$header || exit 1; \
	done
	@echo "All headers validated successfully"

# Clean build artifacts
clean:
	rm -f $(TEST_OBJECTS)
	rm -f $(IMPL_OBJECTS)
	rm -f $(TEST_EXECUTABLE)
	rm -f *.o tests/*.o pipeline/*.o

# Clean everything (same as clean)
distclean: clean

# Install dependencies (none needed for this module)
deps:
	@echo "No external dependencies required for VideoProcessor tests"
	@echo "Uses existing project dependencies (ChunkProcessor, IIQStream, etc.)"

# Help target
help:
	@echo "VideoProcessor Test Makefile"
	@echo "============================"
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build and run all tests (default)"
	@echo "  build        - Build test executable only"
	@echo "  test         - Build and run all tests"
	@echo "  verify       - Quick verification with limited output"
	@echo "  perf         - Run performance tests only"
	@echo "  validate-header - Validate header syntax"
	@echo "  clean        - Remove build artifacts"
	@echo "  distclean    - Same as clean"
	@echo "  deps         - Show dependency information"
	@echo "  help         - Show this help message"
	@echo ""
	@echo "Examples:"
	@echo "  make test    - Run all tests"
	@echo "  make verify  - Quick check"
	@echo "  make clean   - Clean up"

# Dependencies for object files
$(TEST_DIR)/test_runner.o: $(TEST_DIR)/test_runner.cpp
$(TEST_DIR)/test_video_processor.o: $(TEST_DIR)/test_video_processor.cpp video_processor.h
$(TEST_DIR)/test_iq_acquisition_node.o: $(TEST_DIR)/test_iq_acquisition_node.cpp pipeline/iq_acquisition_node.h
video_processor.o: video_processor.cpp video_processor.h
pipeline/iq_acquisition_node.o: pipeline/iq_acquisition_node.cpp pipeline/iq_acquisition_node.h pipeline/iq_acquisition_node_types.h
