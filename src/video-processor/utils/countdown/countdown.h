#pragma once
#include <cstdint>

template<typename T = uint32_t>
class Countdown {
public:
  explicit Countdown(T countTo, bool reset = false) : countTo_(countTo), current_(reset ? 0 : countTo) {}
  void reset() { current_ = 0; }
  [[nodiscard]] bool tick() {
    return ++current_ >= countTo_;
  }
  [[nodiscard]] bool tick(T step) {
    current_ += step;
    return current_ >= countTo_;
  }
  [[nodiscard]] bool running() const {
    return current_ < countTo_;
  }
  void stop() {
    current_ = countTo_;
  }
 private:
  T countTo_;
  T current_;
};
