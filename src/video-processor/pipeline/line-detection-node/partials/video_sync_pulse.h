#pragma once
#include "./video_sync_detector.h"

namespace IQVideoProcessor::Pipeline {

struct VideoSyncPulse: VideoSyncDetector::Result {
  double absFallingFrontPosition{0};
  double absRisingFrontPosition{0};
  double absCenterPosition{0};

  static VideoSyncPulse fromSyncResult(const VideoSyncDetector::Result &r, const double absolutePosition) {
    VideoSyncPulse vsp;
    vsp.fallingFrontPosition = r.fallingFrontPosition;
    vsp.risingFrontPosition = r.risingFrontPosition;
    vsp.centerPosition = r.centerPosition;
    vsp.width = r.width;
    vsp.absFallingFrontPosition = r.fallingFrontPosition + absolutePosition;
    vsp.absRisingFrontPosition = r.risingFrontPosition + absolutePosition;
    vsp.absCenterPosition = r.centerPosition + absolutePosition;
    return vsp;
  };
};

}
