#include "../../../video_processor_configs.h"
#include "./segment_ave_filter.h"

namespace IQVideoProcessor::Pipeline {

SegmentAveFilter::SegmentAveFilter(SampleRateType sampleRate): sampleRate_(sampleRate) {
  _ds500kHz_.totalSamples = 0;
}

const AveFilteredDemodulatedSegment& SegmentAveFilter::process(const DemodulatedSegment &segment) {
  if (_ds500kHz_.totalSamples == 0) {
    // Initialize the 500kHz segment with the first segment data
    _ds500kHz_.data.resize(segment.totalSamples);
    _ds500kHz_.totalSamples = segment.totalSamples;
    _ds500kHz_.effectiveSamples = segment.effectiveSamples;
    _ds500kHz_.effectiveOffset = segment.effectiveOffset;
    // Calculating the filter divider based on the sample rate
    _ds500kHz_.halfAveSize = (sampleRate_ / SYNC_DETECTOR_FILTER_HZ) >> 1; // 500kHz
    _ds500kHz_.aveSize = (_ds500kHz_.halfAveSize << 1) + 1; // Force to odd: even -> +1, odd unchanged
  }
  _ds500kHz_.segmentIndex = segment.segmentIndex;
  _ds500kHz_.effectiveStartPosition = segment.effectiveStartPosition;

  const auto aveSize = _ds500kHz_.aveSize;
  const auto halfAveSize = _ds500kHz_.halfAveSize;

  TFloat summ = 0;
  for (size_t i = 0; i < aveSize; ++i) {
    summ += segment.data[i];
  }
  // Fill the start of the segment with the initial sum
  for (size_t i = 0; i <= halfAveSize; ++i) {
    _ds500kHz_.data[i] = summ; // Fill the first half with the initial sum
  }

  auto summLeaveIdx = 0;
  auto summEnterIdx = aveSize;
  auto summWriteIdx = halfAveSize + 1;

  // Sliding window to calculate the sum for the rest of the segment
  while (summEnterIdx < segment.totalSamples) {
    summ -= segment.data[summLeaveIdx++];
    summ += segment.data[summEnterIdx++];
    _ds500kHz_.data[summWriteIdx++] = summ;
  }
  // Fill the rest of the segment with the last sum
  while (summWriteIdx < segment.totalSamples) {
    _ds500kHz_.data[summWriteIdx++] = summ;
  }

  return _ds500kHz_;
}

};
