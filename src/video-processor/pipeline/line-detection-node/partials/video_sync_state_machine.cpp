#include "./video_sync_state_machine.h"

namespace IQVideoProcessor::Pipeline {
VideoSyncStateMachine::VideoSyncStateMachine(const SampleRateType sampleRate): sampleRate_(sampleRate) {

}

inline void VideoSyncStateMachine::setNextState(const State nextState) {
  if (nextState != currentState_) {
    previousState_ = currentState_;
    currentState_ = nextState;
  }
}

inline void VideoSyncStateMachine::setPreviousPulse(const VideoSyncPulse& pulse, VideoSyncPulseType const pulseType) {
  previousPulse_ = pulse;
  previousPulseType_ = pulseType;
}

inline VideoSyncStateMachine::VideoSyncPulseType VideoSyncStateMachine::estimatePulseType(const VideoSyncPulse& pulse) const {
  switch (rangeEstimator_.estimatePulseByWidth(pulse.width)) {
    case EstimatedPulseType::ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE: return VideoSyncPulseType::HORIZONTAL_OR_EQUALIZING_PULSE;
    case EstimatedPulseType::ES_VERTICAL_SYNC_PULSE: return VideoSyncPulseType::VERTICAL_PULSE;
    case EstimatedPulseType::ES_VERTICAL_LONG_SYNC_PULSE: return VideoSyncPulseType::VERTICAL_LONG_PULSE;
    case EstimatedPulseType::ES_UNKNOWN_SYNC_PULSE:
    default: return VideoSyncPulseType::UNKNOWN_PULSE;
  }
}

[[nodiscard]] bool VideoSyncStateMachine::initialized() const {
  return initialized_;
}

void VideoSyncStateMachine::initialize(const VideoStandardDetector::Result& detectedStandard, const EventCallback& eventCallback) {
  ds_ = detectedStandard;
  eventCallback_ = eventCallback;
  if (ds_.standard == UNKNOWN_VIDEO_STANDARD) {
    return;
  }
  initialized_ = true;
  setNextState(WAITING_FIRST_SYNC_PULSE_STATE);
}

void VideoSyncStateMachine::reset() {
  previousState_ = UNKNOWN_STATE;
  currentState_ = UNKNOWN_STATE;
  previousPulseType_ = UNKNOWN_PULSE;
  previousPulse_ = {};
  processedLinesCount_ = 0;
  processedEqPulsesCount_ = 0;
  processedVSyncPulsesCount_ = 0;
}

void VideoSyncStateMachine::process(const std::vector<VideoSyncPulse>& videoSyncPulses, const double processingEndPosition) {
  for (const auto& pulse : videoSyncPulses) {
    processNextSyncPulse(pulse);
  }
}

void VideoSyncStateMachine::processNextSyncPulse(const VideoSyncPulse& pulse) {
  const auto pulseType = estimatePulseType(pulse);

  switch (currentState_) {
    case WAITING_FIRST_SYNC_PULSE_STATE: return handleWaitingFirstSyncPulse(pulse, pulseType);
    case SYNCING_IN_PROGRESS_STATE: return handleSyncingInProgress(pulse, pulseType);
    case PROCESSING_HORIZONTAL_REGION_STATE: return handleProcessingHorizontalRegion(pulse, pulseType);
    case PROCESSING_EQUALIZING_SYNCH_REGION_STATE: return handleProcessingEqualizingSyncRegion(pulse, pulseType);
    case PROCESSING_VERTICAL_SYNC_REGION_STATE: return handleProcessingVerticalSyncRegion(pulse, pulseType);
    case UNKNOWN_STATE:
    break;
  }
}

inline void VideoSyncStateMachine::handleWaitingFirstSyncPulse(const VideoSyncPulse& pulse, const VideoSyncPulseType pulseType) {
  if (pulseType == UNKNOWN_PULSE) {
    return;
  }
  setPreviousPulse(pulse, pulseType);
  setNextState(SYNCING_IN_PROGRESS_STATE);
}

inline void VideoSyncStateMachine::handleSyncingInProgress(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {
  if (pulseType == UNKNOWN_PULSE) { // Invalid pulse or distance, lets find another first pulse
    setNextState(WAITING_FIRST_SYNC_PULSE_STATE);
    return;
  }

  const auto distance = pulse.absCenterPosition - previousPulse_.absCenterPosition;
  const auto distanceType = rangeEstimator_.estimateSignalDistance(distance);

  if (distanceType == ES_HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by horizontal distance after any other pulse.
    // It could be horizontal or the first equalizing pulse right after the last horizontal pulse.
    switch (rangeEstimator_.comparePulseByWidth(previousPulse_.width, pulse.width)) {
      case LEFT_EQUAL_TO_RIGHT: // Equal widths, so it seems like horizontal
        return enterProcessingHorizontalRegionState(pulse);
      case LEFT_GREATER_THAN_RIGHT: // Previous pulse is longer than the current one, consider it equalizing
        return enterProcessingEqualizingSyncRegionState(pulse);
      default: // Not possible, but just in case
        return;
    }
  }
  if (distanceType == ES_HALF_HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by half horizontal distance after any other pulse, consider it equalizing
    return enterProcessingEqualizingSyncRegionState(pulse);
  }
  if (distanceType == ES_HALF_HORIZONTAL_DISTANCE && pulseType == VERTICAL_PULSE) {
    // Having a vertical pulse, followed by half horizontal distance after the previous pulse,
    // consider it vertical somewhere in the middle of ver
    return enterProcessingVerticalSyncRegionState(pulse, previousPulseType_ != VERTICAL_PULSE);
  }
  if (distanceType == ES_70PERCENT_HORIZONTAL_DISTANCE && pulseType == VERTICAL_PULSE) {
    // Having a vertical pulse, followed by ~70% horizontal distance after the previous pulse,
    // consider it first vertical pulse right after the last equalizing pulse
    return enterProcessingVerticalSyncRegionState(pulse, true);
  }
  if (distanceType == ES_33PERCENT_HORIZONTAL_DISTANCE && pulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // Having pulse, followed by ~33% horizontal distance after the previous pulse,
    // consider it first equalizing pulse right after the last vertical pulse
    return enterProcessingEqualizingSyncRegionState(pulse);
  }

  setNextState(WAITING_FIRST_SYNC_PULSE_STATE); // Unable to classify, start over
}

inline void VideoSyncStateMachine::enterProcessingHorizontalRegionState(const VideoSyncPulse& pulse) {
  processedLinesCount_ = 0;
  setPreviousPulse(pulse, VideoSyncPulseType::HORIZONTAL_SYNC_PULSE); // Classify the current pulse as horizontal
  setNextState(PROCESSING_HORIZONTAL_REGION_STATE);
}

inline void VideoSyncStateMachine::enterProcessingEqualizingSyncRegionState(const VideoSyncPulse& pulse) {
  processedEqPulsesCount_ = 1;
  setPreviousPulse(pulse, VideoSyncPulseType::EQUALIZING_PULSE);
  setNextState(PROCESSING_EQUALIZING_SYNCH_REGION_STATE);
}

inline void VideoSyncStateMachine::enterProcessingVerticalSyncRegionState(const VideoSyncPulse& pulse, const bool isFirst) {
  // We can detect if it's a first or a second subsequent pulse only for vertical pulses,
  // because they have deterministic timings in compare to horizontal and equalizing pulses
  processedVSyncPulsesCount_ = isFirst ? 1 : 2;
  setPreviousPulse(pulse, VideoSyncPulseType::VERTICAL_PULSE);
  setNextState(PROCESSING_VERTICAL_SYNC_REGION_STATE);
}

inline void VideoSyncStateMachine::handleProcessingHorizontalRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {

}

inline void VideoSyncStateMachine::handleProcessingEqualizingSyncRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {

}

inline void VideoSyncStateMachine::handleProcessingVerticalSyncRegion(const VideoSyncPulse& pulse, VideoSyncPulseType pulseType) {

}

// Helper methods for event generation
inline void VideoSyncStateMachine::sendEvent(const EventData& eventData) {
  if (eventCallback_) {
    eventCallback_(eventData);
  }
}

inline void VideoSyncStateMachine::sendLineDetectedEvent(uint32_t lineNumber, const VideoSyncPulse& pulse) {
  EventData event;
  event.type = LINE_DETECTED;
  event.lineNumber = lineNumber;
  sendEvent(event);
}

inline void VideoSyncStateMachine::sendEqualizationPulseEvent(uint32_t pulseNumber, const VideoSyncPulse& pulse) {
  EventData event;
  event.type = EQUALIZATION_PULSE;
  event.pulseNumber = 0;
  sendEvent(event);
}

inline void VideoSyncStateMachine::sendFrameBeginEvent() {
  EventData event;
  event.type = FRAME_BEGIN;
  sendEvent(event);
}

inline void VideoSyncStateMachine::sendFrameEndEvent() {
  EventData event;
  event.type = FRAME_END;
  sendEvent(event);
}

}
