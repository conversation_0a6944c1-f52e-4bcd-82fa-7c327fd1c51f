#pragma once
#include "./sync_pulse_locator.h"
#include "./sync-encoders/ntsc-encoder.h"
#include "../video_standard_detector.h"
#include <functional>
#include <tuple>

namespace IQVideoProcessor::Pipeline {

using namespace SyncEncoders;

class SyncOrchestrator {
public:
  enum EventType {
    UNKNOWN_EVENT = 0,
    FRAME_BEGIN = 10,
    EQUALIZATION_DETECTED = 20,
    LINE_DETECTED = 30,
    FRAME_END = 40,
  };

  struct EventData {
    VideoStandard standard{UNKNOWN_VIDEO_STANDARD};
    EventType type{UNKNOWN_EVENT};
    bool isOdd{true};
    double lineDistance_{0};                // in samples
    double fromAbsPosition_{0};             // absolute position (center-to-center start)
    double toAbsPosition_{0};
    size_t lineNumber{0};                   // number of visible lines detected in the current frame
    uint64_t uninterruptedFrameSeries{0};   // number of uninterrupted frames since last lock
  };

  using EventCallback = std::function<void(const EventData&)>;

  explicit SyncOrchestrator(SampleRateType sampleRate);

  void initialize(const VideoStandardDetector::Result& detectedStandard, EventCallback eventCallback);
  [[nodiscard]] bool initialized() const;

  void process(const std::vector<VideoSyncPulse>& pulses, double processingEndPosition);
  void reset();

private:
  [[nodiscard]] std::tuple<bool, size_t> tryLockOnVertical(const std::vector<VideoSyncPulse>& pulses) const;
  [[nodiscard]] inline bool canProcessCurrentEncoderItem(double processingEndPosition) const;
  inline void processCurrentEncoderItem(SyncPulseLocator &pulseLocator);
  inline void processDefaultFuture();
  inline void processEncoderTransition(Transition transition, double nextProcessingPosition);
  inline static EstimatedPulseType toEstimatedPulseType(VideoSyncPulseType pulseType) ;

  // Event emission helper methods
  void emitEvent(EventType type, double fromPosition, double toPosition);
  void checkAndEmitFrameBegin(const EncoderItem& currentItem, const EncoderItem& nextItem, double fromPosition);
  void checkAndEmitFrameEnd(const EncoderItem& currentItem, const EncoderItem& nextItem, double fromPosition);
  void checkAndEmitLineDetected(const EncoderItem& currentItem, double fromPosition, double toPosition);
  void checkAndEmitEqualizationDetected(const EncoderItem& currentItem, double fromPosition, double toPosition);
  [[nodiscard]] bool isEnteringHorizontalRegion(const EncoderItem& currentItem, const EncoderItem& nextItem) const;
  [[nodiscard]] bool isLeavingHorizontalRegion(const EncoderItem& currentItem, const EncoderItem& nextItem) const;

private:
  // Config
  SampleRateType sampleRate_;
  EventCallback callback_{};
  VideoStandardDetector::Result detectedStandard_{};
  SignalRangeEstimator rangeEstimator_{sampleRate_};

  // Encoder
  std::unique_ptr<VideoSyncEncoder> videoSyncEncoder_{nullptr};

  // Runtime state
  bool initialized_{false};
  bool locked_{false};
  double currentProcessingPosition_{0.0};

  // Event tracking state
  bool frameActive_{false};
  bool currentFrameIsOdd_{true};
  size_t currentVisibleLineNumber_{0};
  size_t equalizationPulseCount_{0};
  uint64_t uninterruptedFrameCount_{0};
  FrameRegion lastRegion_{FrameRegion::HORIZONTAL_LINES};
  double lastFrameBeginPosition_{0.0};
};

}
