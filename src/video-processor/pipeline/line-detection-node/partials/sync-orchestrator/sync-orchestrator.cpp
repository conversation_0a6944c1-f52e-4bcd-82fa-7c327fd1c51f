#include "./sync-orchestrator.h"
#include <iostream>

namespace IQVideoProcessor::Pipeline {

SyncOrchestrator::SyncOrchestrator(const SampleRateType sampleRate) : sampleRate_(sampleRate) {

}

void SyncOrchestrator::initialize(const VideoStandardDetector::Result& detectedStandard, EventCallback eventCallback) {
  detectedStandard_ = detectedStandard;
  callback_ = std::move(eventCallback);

  if (detectedStandard_.standard == UNKNOWN_VIDEO_STANDARD) {
    std::cerr << "SyncOrchestrator: Initialization failed - Unknown video standard" << std::endl;
    initialized_ = false;
    return;
  }

  if (detectedStandard_.horizontalLineDuration <= 0) {
    std::cerr << "SyncOrchestrator: Initialization failed - Invalid horizontal line duration: "
              << detectedStandard_.horizontalLineDuration << std::endl;
    initialized_ = false;
    return;
  }

  VideoSyncEncoder::Config config {
    .standard = detectedStandard_.standard,
    .sampleRate = sampleRate_
  };
  videoSyncEncoder_ = std::make_unique<NTSCSyncEncoder>(config);

  // Initialize event tracking state
  frameActive_ = false;
  currentFrameIsOdd_ = true;
  currentVisibleLineNumber_ = 0;
  equalizationPulseCount_ = 0;
  uninterruptedFrameCount_ = 0;
  lastRegion_ = FrameRegion::HORIZONTAL_LINES;
  lastFrameBeginPosition_ = 0.0;

  initialized_ = true;
}

bool SyncOrchestrator::initialized() const {
  return initialized_;
}

void SyncOrchestrator::reset() {
  if (videoSyncEncoder_) {
    videoSyncEncoder_->reset();
  }
  locked_ = false;

  // Reset event tracking state
  frameActive_ = false;
  currentFrameIsOdd_ = true;
  currentVisibleLineNumber_ = 0;
  equalizationPulseCount_ = 0;
  uninterruptedFrameCount_ = 0;
  lastRegion_ = FrameRegion::HORIZONTAL_LINES;
  lastFrameBeginPosition_ = 0.0;
}

void SyncOrchestrator::process(const std::vector<VideoSyncPulse>& pulses, const double processingEndPosition) {
  if (!initialized_ || !videoSyncEncoder_) return;

  size_t fromPulsesIdx = 0;
  if (!locked_) {
    if (const auto [locked, lockedPulseIdx] = tryLockOnVertical(pulses); locked) {
      std::cout << "SyncOrchestrator: ✓ Initial vertical lock acquired at position " << pulses[lockedPulseIdx].absCenterPosition << ", starting processing from next pulse." << std::endl;
      fromPulsesIdx = lockedPulseIdx + 1; // Start processing from the pulse we locked on
      currentProcessingPosition_ = pulses[lockedPulseIdx].absCenterPosition;
      locked_ = true;
    } else {
      return; // Still not locked, wait for next segment
    }
  }

  SyncPulseLocator pulseLocator(pulses, fromPulsesIdx);
  while (canProcessCurrentEncoderItem(processingEndPosition)) {
    // We have enough room/samples to process the current video sync encoder item
    processCurrentEncoderItem(pulseLocator);
  }

}

std::tuple<bool, size_t> SyncOrchestrator::tryLockOnVertical(const std::vector<VideoSyncPulse>& pulses) const {
  const VideoSyncPulse *previousPulse = nullptr;
  EstimatedPulseType prevPulseType = ES_UNKNOWN_SYNC_PULSE;

  auto lockOn = [this](const Transition transition, const size_t processed) -> std::tuple<bool, size_t> {
    videoSyncEncoder_->reset();
    videoSyncEncoder_->transit(transition);
    return { true, processed };
  };

  for (size_t processed = 0; processed < pulses.size(); ++processed ) {
    auto& pulse = pulses[processed];
    const auto pulseType = rangeEstimator_.estimatePulseByWidth(pulse.width);
    if (previousPulse == nullptr) {
      previousPulse = &pulse;
      prevPulseType = pulseType;
      continue;
    }
    const auto distance = static_cast<TFloat>(pulse.absCenterPosition - previousPulse->absCenterPosition);
    // Look for the pattern: Equalizing -> Vertical, about 70% of horizontal line distance
    if (
      prevPulseType == ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE
      && pulseType == ES_VERTICAL_SYNC_PULSE &&
      rangeEstimator_.is70PercentHorizontalLineDistance(distance)) {
      return lockOn(Transition::TO_ODD_VERTICAL_START, processed); // Found vertical sync pulse, lock on
    }
    // Look for the pattern: Vertical -> Equalizing, about 33% of horizontal line distance
    if (prevPulseType == ES_VERTICAL_SYNC_PULSE &&
      pulseType == ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE &&
      rangeEstimator_.is33PercentHorizontalLineDistance(distance)) {
      return lockOn(Transition::TO_ODD_VERTICAL_LAST, processed - 1); // Found equalizing after vertical, lock on previous vertical
    }

    previousPulse = &pulse;
    prevPulseType = pulseType;
  }

  return { false, pulses.size() - 1 };
}

inline EstimatedPulseType SyncOrchestrator::toEstimatedPulseType(const VideoSyncPulseType pulseType) {
  switch (pulseType) {
  case HORIZONTAL_SYNC_PULSE:
  case EQUALIZING_SYNC_PULSE:
    return ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE;
  case VERTICAL_SYNC_PULSE:
    return ES_VERTICAL_SYNC_PULSE;
  case UNKNOWN_PULSE:
    return ES_UNKNOWN_SYNC_PULSE;
  default:
    throw std::invalid_argument("Unknown pulse type");
  }
}

inline bool SyncOrchestrator::canProcessCurrentEncoderItem(const double processingEndPosition) const {
  return currentProcessingPosition_ + videoSyncEncoder_->currentItem().maxFutureDistance <= processingEndPosition;
}

inline void SyncOrchestrator::processDefaultFuture() {
  const auto& encoderItem = videoSyncEncoder_->currentItem();
  const auto defaultFuture = encoderItem.futures[encoderItem.defaultFutureIdx];
  processEncoderTransition(defaultFuture.transition, currentProcessingPosition_ + defaultFuture.defaultDistance);
}

inline void SyncOrchestrator::processEncoderTransition(const Transition transition, const double nextProcessingPosition) {
  auto currentProcessingPosition = currentProcessingPosition_;
  const auto& currentItem = videoSyncEncoder_->currentItem();

  // Apply the transition first to get the next item
  currentProcessingPosition_ = nextProcessingPosition;
  videoSyncEncoder_->transit(transition);
  const auto& nextItem = videoSyncEncoder_->currentItem();

  // Store the previous region for comparison
  lastRegion_ = currentItem.region;

  // Check for frame boundaries and emit events
  checkAndEmitFrameBegin(currentItem, nextItem, currentProcessingPosition);
  checkAndEmitFrameEnd(currentItem, nextItem, currentProcessingPosition);

  // Emit events based on the current item being processed
  checkAndEmitEqualizationDetected(currentItem, currentProcessingPosition, nextProcessingPosition);
  checkAndEmitLineDetected(currentItem, currentProcessingPosition, nextProcessingPosition);
}

inline void SyncOrchestrator::processCurrentEncoderItem(SyncPulseLocator &pulseLocator) {
  const auto& encoderItem = videoSyncEncoder_->currentItem();
  // Look for the next pulse in range
  const auto searchResult = pulseLocator.findNextInRangeByAbsCenterPosition(currentProcessingPosition_, currentProcessingPosition_ + encoderItem.maxFutureDistance);
  if (!searchResult.has_value()) { // No matching pulse found in range, processing the default future, automatically predicted by encoder
    return processDefaultFuture();
  }
  // Found a matching pulse, process it
  const auto& currentProcessingPulse = *(searchResult.value());
  const auto distance = currentProcessingPulse.absCenterPosition - currentProcessingPosition_;
  if (distance <= 0) {
    return processDefaultFuture(); // Should not happen, but just in case
  }
  // Check if any of the futures match this pulse by type and distance
  const auto pulseType = rangeEstimator_.estimatePulseByWidth(currentProcessingPulse.width);
  for (const auto& future : encoderItem.futures) {
    if (toEstimatedPulseType(future.pulseType) == pulseType && future.inRange.inRange(distance)) {
      // Nice, now we can evaluate the phase correction, but skip it for now
      // const auto deltaDistance = currentProcessingPulse.absCenterPosition - (currentProcessingPosition_ + future.defaultDistance);
      // std:: cout << "SyncOrchestrator: Phase correction of " << deltaDistance << " samples applied at position " << currentProcessingPulse.absCenterPosition << std::endl;
      return processEncoderTransition(future.transition, currentProcessingPulse.absCenterPosition); // Move exactly to the pulse center position
    }
  }
  // We have a pulse, but it doesn't match any future by type/distance. This means we are out of sync, or receive some unpredicted pulse.
  // TODO: Accumulate the out-of-sync statistics, and if too many, reset the orchestrator to initial state and wait for lock again
  std::cout << "SyncOrchestrator: Unmatched pulse at position " << currentProcessingPulse.absCenterPosition << ", processing default future." << std::endl;

  return processDefaultFuture();
}

void SyncOrchestrator::emitEvent(EventType type, double fromPosition, double toPosition) {
  if (!callback_) return;

  EventData event;
  event.standard = detectedStandard_.standard;
  event.type = type;
  event.isOdd = currentFrameIsOdd_;
  event.lineDistance_ = detectedStandard_.horizontalLineDuration;
  event.fromAbsPosition_ = fromPosition;
  event.toAbsPosition_ = toPosition;
  event.lineNumber = currentVisibleLineNumber_;
  event.uninterruptedFrameSeries = uninterruptedFrameCount_;

  callback_(event);
}

void SyncOrchestrator::checkAndEmitFrameBegin(const EncoderItem& currentItem, const EncoderItem& nextItem, double fromPosition) {
  // Emit FRAME_BEGIN when entering horizontal region from vertical/equalizing regions
  if (isEnteringHorizontalRegion(currentItem, nextItem)) {
    frameActive_ = true;
    currentFrameIsOdd_ = nextItem.odd;
    currentVisibleLineNumber_ = 0;
    equalizationPulseCount_ = 0;
    lastFrameBeginPosition_ = fromPosition;
    uninterruptedFrameCount_++;

    emitEvent(FRAME_BEGIN, fromPosition, fromPosition);
  }
}

void SyncOrchestrator::checkAndEmitFrameEnd(const EncoderItem& currentItem, const EncoderItem& nextItem, double fromPosition) {
  // Emit FRAME_END when leaving horizontal region to enter pre-equalizing or vertical regions
  if (isLeavingHorizontalRegion(currentItem, nextItem)) {
    frameActive_ = false;
    emitEvent(FRAME_END, fromPosition, fromPosition);
  }
}

void SyncOrchestrator::checkAndEmitLineDetected(const EncoderItem& currentItem, double fromPosition, double toPosition) {
  // Emit LINE_DETECTED when processing visible horizontal lines
  if (frameActive_ && currentItem.visible && currentItem.region == FrameRegion::HORIZONTAL_LINES) {
    currentVisibleLineNumber_ = currentItem.visibleLineIndex;
    emitEvent(LINE_DETECTED, fromPosition, toPosition);
  }
}

void SyncOrchestrator::checkAndEmitEqualizationDetected(const EncoderItem& currentItem, double fromPosition, double toPosition) {
  // Emit EQUALIZATION_DETECTED when processing equalizing pulses
  if (currentItem.pulseType == EQUALIZING_SYNC_PULSE &&
      (currentItem.region == FrameRegion::POST_EQUALIZING || currentItem.region == FrameRegion::PRE_EQUALIZING)) {
    equalizationPulseCount_++;
    emitEvent(EQUALIZATION_DETECTED, fromPosition, toPosition);
  }
}

bool SyncOrchestrator::isEnteringHorizontalRegion(const EncoderItem& currentItem, const EncoderItem& nextItem) const {
  return (currentItem.region != FrameRegion::HORIZONTAL_LINES) &&
         (nextItem.region == FrameRegion::HORIZONTAL_LINES);
}

bool SyncOrchestrator::isLeavingHorizontalRegion(const EncoderItem& currentItem, const EncoderItem& nextItem) const {
  return (currentItem.region == FrameRegion::HORIZONTAL_LINES) &&
         (nextItem.region != FrameRegion::HORIZONTAL_LINES);
}

}