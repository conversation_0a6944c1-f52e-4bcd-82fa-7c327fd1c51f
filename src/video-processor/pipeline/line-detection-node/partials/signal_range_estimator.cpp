#include "./signal_range_estimator.h"
#include "../../../video_processor_configs.h"

#include <algorithm>

namespace IQVideoProcessor::Pipeline {

SignalRangeEstimator::SignalRangeEstimator(const SampleRateType sampleRate): sampleRate_(sampleRate) {
  horizontalOrEqualizingPulseWidthRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_OR_EQ_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_OR_EQ_PULSE_MAX_WIDTH_SEC)
  };
  verticalSyncPulseWidthRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_PULSE_MAX_WIDTH_SEC)
  };
  verticalSyncLongPulseWidthRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_LONG_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_LONG_PULSE_MAX_WIDTH_SEC)
  };
  horizontalLineDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_DISTANCE_MIN_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_DISTANCE_MAX_SEC)
  };
  horizontalLineHalfDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_HALF_DISTANCE_MIN_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_HALF_DISTANCE_MAX_SEC)
  };
  horizontalLine70PercentDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_70PERCENT_DISTANCE_MIN_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_70PERCENT_DISTANCE_MAX_SEC)
  };
  horizontalLine33PercentDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_33PERCENT_DISTANCE_MIN_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_LINE_33PERCENT_DISTANCE_MAX_SEC)
  };
}

[[nodiscard]] EstimatedPulseType SignalRangeEstimator::estimatePulseByWidth(const TFloat pulseWidth) const {
  if (horizontalOrEqualizingPulseWidthRange_.inRange(pulseWidth)) {
    return ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE; // or EQUALIZING_PULSE, need more context to differentiate
  }
  if (verticalSyncPulseWidthRange_.inRange(pulseWidth)) {
    return ES_VERTICAL_SYNC_PULSE;
  }
  if (verticalSyncLongPulseWidthRange_.inRange(pulseWidth)) {
    return ES_VERTICAL_LONG_SYNC_PULSE;
  }
  return ES_UNKNOWN_SYNC_PULSE;
}

[[nodiscard]] EstimatedDistanceType SignalRangeEstimator::estimateSignalDistance(const TFloat distance) const {
  if (horizontalLineDistanceRange_.inRange(distance)) {
    return ES_HORIZONTAL_DISTANCE;
  }
  if (horizontalLineHalfDistanceRange_.inRange(distance)) {
    return ES_HALF_HORIZONTAL_DISTANCE;
  }
  if (horizontalLine70PercentDistanceRange_.inRange(distance)) {
    return ES_70PERCENT_HORIZONTAL_DISTANCE;
  }
  if (horizontalLine33PercentDistanceRange_.inRange(distance)) {
    return ES_33PERCENT_HORIZONTAL_DISTANCE;
  }
  return ES_UNKNOWN_DISTANCE;
}

[[nodiscard]] bool SignalRangeEstimator::isHorizontalLineDistance(const TFloat distance) const {
  return horizontalLineDistanceRange_.inRange(distance);
}

[[nodiscard]] bool SignalRangeEstimator::isHalfHorizontalLineDistance(const TFloat distance) const {
  return horizontalLineHalfDistanceRange_.inRange(distance);
}

[[nodiscard]] bool SignalRangeEstimator::is70PercentHorizontalLineDistance(const TFloat distance) const {
  return horizontalLine70PercentDistanceRange_.inRange(distance);
}

[[nodiscard]] bool SignalRangeEstimator::is33PercentHorizontalLineDistance(const TFloat distance) const {
  return horizontalLine33PercentDistanceRange_.inRange(distance);
}

[[nodiscard]] bool SignalRangeEstimator::areEqualPulsesByWidth(const TFloat pulseWidth1, const TFloat pulseWidth2) const {
  const TFloat delta = std::abs(pulseWidth1 - pulseWidth2);
  const TFloat maxAllowedDelta = std::min(pulseWidth1, pulseWidth2) * static_cast<TFloat>(0.1); // 10% tolerance
  return delta <= maxAllowedDelta;
}

[[nodiscard]] PulseComparison SignalRangeEstimator::comparePulseByWidth(const TFloat pulseWidth1, const TFloat pulseWidth2) const {
  if (areEqualPulsesByWidth(pulseWidth1, pulseWidth2)) {
    return LEFT_EQUAL_TO_RIGHT;
  }
  return (pulseWidth1 < pulseWidth2) ? LEFT_LESS_THAN_RIGHT : LEFT_GREATER_THAN_RIGHT;
}

} // namespace IQVideoProcessor
