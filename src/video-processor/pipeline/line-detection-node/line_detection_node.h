#pragma once
#include "../../../stream-pipeline/stream_node.h"
#include "../../../types.h"
#include "../../utils/countdown/countdown.h"
#include "../iq_demodulation_node_types.h"
#include "./partials/segment_ave_filter.h"
#include "./partials/segment_pulses_detector.h"
#include "./partials/video_standard_detector.h"
#include "./partials/video_sync_detector.h"
#include "./partials/sync-orchestrator/sync-orchestrator.h"

namespace IQVideoProcessor::Pipeline {

class LineDetectionNode final : public SPipeline::StreamNode<DemodulatedSegment, DemodulatedSegment> {
public:
  explicit LineDetectionNode(SampleRateType sampleRate);
  ~LineDetectionNode() override;

private:
  bool process(DemodulatedSegment& segment) override;

  struct SegmentProcessingConfig: VideoSyncDetector::Config {
    TFloat signalMinVal;
    TFloat signalMaxVal;
    TFloat signalScale;
  };

  SampleRateType sampleRate_;
  SegmentAveFilter segmentSyncDetectionFilter_;
  SegmentPulsesDetector segmentPulsesDetector_{sampleRate_};
  SignalRangeEstimator signalRangeEstimator_{sampleRate_};
  VideoStandardDetector videoStandardDetector_{sampleRate_};
  Countdown<> standardDetectorRetryCountdown_{sampleRate_};
  SyncOrchestrator syncOrchestrator_{sampleRate_};

  // Pointer to the current segment being processed, for reference in callbacks
  DemodulatedSegment* currentSegment_{nullptr};
};

} // namespace IQVideoProcessor::Pipeline
