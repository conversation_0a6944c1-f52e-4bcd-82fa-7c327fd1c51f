#pragma once

#include <vector>
#include <complex>
#include "../../types.h"

namespace IQVideoProcessor::Pipeline {

// Holds a segment of IQ samples with overlap guards around an effective region.
struct ComplexIQSegment {
  std::vector<std::complex<TFloat>> data;
  size_t totalSamples;
  size_t effectiveSamples;
  size_t effectiveOffset;
  uint64_t effectiveStartPosition;
  size_t segmentIndex;
};

} // namespace IQVideoProcessor::Pipeline
