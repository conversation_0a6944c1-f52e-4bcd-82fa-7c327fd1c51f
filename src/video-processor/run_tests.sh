#!/bin/bash

# Test runner script for VideoProcessor
# BladeRF Video Decoding Project - VideoProcessor Module

set -e  # Exit on any error

echo "VideoProcessor Test Runner"
echo "=========================="
echo "Comprehensive test coverage for video processing pipeline"
echo ""

# Check if we're in the right directory
if [ ! -f "video_processor.h" ]; then
    echo "Error: Must be run from src/video-processor directory"
    echo "Usage: cd src/video-processor && ./run_tests.sh"
    exit 1
fi

# Parse command line arguments
QUICK_MODE=false
PERFORMANCE_ONLY=false
VERBOSE_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --quick|-q)
            QUICK_MODE=true
            shift
            ;;
        --perf|-p)
            PERFORMANCE_ONLY=true
            shift
            ;;
        --verbose|-v)
            VERBOSE_MODE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --quick, -q      Run quick verification only"
            echo "  --perf, -p       Run performance tests only"
            echo "  --verbose, -v    Run with verbose output"
            echo "  --help, -h       Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0               Run all tests"
            echo "  $0 --quick       Quick verification"
            echo "  $0 --perf        Performance tests only"
            echo "  $0 --verbose     Verbose output"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Color output functions
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "\033[32m✓ $message\033[0m"
            ;;
        "ERROR")
            echo -e "\033[31m✗ $message\033[0m"
            ;;
        "INFO")
            echo -e "\033[34mℹ $message\033[0m"
            ;;
        "WARNING")
            echo -e "\033[33m⚠ $message\033[0m"
            ;;
    esac
}

# Clean previous builds
print_status "INFO" "Cleaning previous builds..."
make clean > /dev/null 2>&1

# Validate headers first
print_status "INFO" "Validating VideoProcessor headers..."
if make validate-header > /dev/null 2>&1; then
    print_status "SUCCESS" "Header validation passed"
else
    print_status "ERROR" "Header validation failed"
    exit 1
fi

# Build tests
print_status "INFO" "Building VideoProcessor tests..."
if make build > /dev/null 2>&1; then
    print_status "SUCCESS" "Build completed successfully"
else
    print_status "ERROR" "Build failed"
    exit 1
fi

# Run tests based on mode
if [ "$PERFORMANCE_ONLY" = true ]; then
    print_status "INFO" "Running performance benchmarks only..."
    echo ""
    make perf
elif [ "$QUICK_MODE" = true ]; then
    print_status "INFO" "Running quick verification..."
    echo ""
    make verify
elif [ "$VERBOSE_MODE" = true ]; then
    print_status "INFO" "Running all tests in verbose mode..."
    echo ""
    make test
else
    print_status "INFO" "Running all tests with standard output..."
    echo ""
    make test
fi

# Capture exit code
TEST_EXIT_CODE=$?

echo ""
echo "============================="
if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_status "SUCCESS" "All VideoProcessor tests completed successfully!"
    echo ""
    echo "📊 Test Summary:"
    echo "   ✅ VideoProcessor core functionality"
    echo "   ✅ IQAcquisitionNode pipeline tests"
    echo "   ✅ Integration tests"
    echo "   ✅ Performance benchmarks"
    echo "   ✅ Memory tests"
    echo "   ✅ Specification compliance"
    echo ""
    echo "🎉 VideoProcessor module is ready for production use!"
else
    print_status "ERROR" "Some VideoProcessor tests failed!"
    echo ""
    echo "❌ Please review the test output above and fix any issues."
    echo "   Common issues:"
    echo "   - Missing dependencies"
    echo "   - Compilation errors"
    echo "   - Logic errors in implementation"
    echo "   - Memory management issues"
fi

echo "============================="

# Clean up on exit
trap 'echo "Cleaning up..."; make clean > /dev/null 2>&1' EXIT

exit $TEST_EXIT_CODE
