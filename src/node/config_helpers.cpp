#include "config_helpers.h"

#define DEFAULT_FRAME_WIDTH_PX 640
#define DEFAULT_RAW_QUEUE_DEPTH 128
#define DEFAULT_DEMOD_QUEUE_DEPTH 128
#define DEFAULT_LINES_QUEUE_DEPTH 64

namespace NodeHelpers::Config {

IQVideoStream::ProcessingConfig parseVideoProcessingConfig(v8::Isolate* isolate, v8::Local<v8::Object> processingObj) {
    IQVideoStream::ProcessingConfig config;
    v8::Local<v8::Context> context = isolate->GetCurrentContext();

    // Center offset
    v8::Local<v8::Value> centerOffsetVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "centerOffsetHz").ToLocalChecked()).ToLocalChecked();
    if (centerOffsetVal->IsNumber()) {
        config.centerOffsetHz = centerOffsetVal->NumberValue(context).FromJust();
    } else {
        config.centerOffsetHz = 0.0;  // default
    }

    // Slice strategy
    v8::Local<v8::Value> sliceStrategyVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "sliceStrategy").ToLocalChecked()).ToLocalChecked();
    if (sliceStrategyVal->IsString()) {
        v8::String::Utf8Value sliceStrategyStr(isolate, sliceStrategyVal);
        config.sliceStrategy = std::string(*sliceStrategyStr);
    } else {
        config.sliceStrategy = "auto_ntsc";  // default
    }

    // Frame width
    v8::Local<v8::Value> frameWidthVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "frameWidthPx").ToLocalChecked()).ToLocalChecked();
    if (frameWidthVal->IsNumber()) {
        config.frameWidthPx = frameWidthVal->Uint32Value(context).FromJust();
    } else {
        config.frameWidthPx = DEFAULT_FRAME_WIDTH_PX;
    }

    // Queue depths
    v8::Local<v8::Value> queueDepthVal = processingObj->Get(context, v8::String::NewFromUtf8(isolate, "queueDepth").ToLocalChecked()).ToLocalChecked();
    if (queueDepthVal->IsObject()) {
        v8::Local<v8::Object> queueDepthObj = queueDepthVal.As<v8::Object>();
        
        v8::Local<v8::Value> rawVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "raw").ToLocalChecked()).ToLocalChecked();
        if (rawVal->IsNumber()) {
            config.queueDepth.raw = rawVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.raw = DEFAULT_RAW_QUEUE_DEPTH;  // default
        }
        
        v8::Local<v8::Value> demodVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "demod").ToLocalChecked()).ToLocalChecked();
        if (demodVal->IsNumber()) {
            config.queueDepth.demod = demodVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.demod = DEFAULT_DEMOD_QUEUE_DEPTH;  // default
        }
        
        v8::Local<v8::Value> linesVal = queueDepthObj->Get(context, v8::String::NewFromUtf8(isolate, "lines").ToLocalChecked()).ToLocalChecked();
        if (linesVal->IsNumber()) {
            config.queueDepth.lines = linesVal->Uint32Value(context).FromJust();
        } else {
            config.queueDepth.lines = DEFAULT_LINES_QUEUE_DEPTH;  // default
        }
    } else {
        // Default queue depths
        config.queueDepth.raw = DEFAULT_RAW_QUEUE_DEPTH;
        config.queueDepth.demod = DEFAULT_DEMOD_QUEUE_DEPTH;
        config.queueDepth.lines = DEFAULT_LINES_QUEUE_DEPTH;
    }

    return config;
}

} // namespace ConfigHelpers
