#pragma once

#include "../video-converter/processing_config.h"
#include <v8.h>

namespace NodeHelpers::Config {
/**
 * Parse video processing configuration from JavaScript object
 * @param isolate V8 isolate
 * @param processingObj JavaScript object containing configuration
 * @return Parsed VideoProcessingConfig structure
 */
IQVideoStream::ProcessingConfig parseVideoProcessingConfig(v8::Isolate* isolate, v8::Local<v8::Object> processingObj);

} // namespace ConfigHelpers
