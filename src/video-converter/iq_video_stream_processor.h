#pragma once

#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <string>
#include <thread>
#include "../iiq-stream/iiq_stream.h"
#include "../types.h"
#include "./processing_config.h"
#include "./processing_helpers.h"
#include "../chunk-processor/chunk_processor.h"

namespace IQVideoStream {

/**
 * Stop reason enumeration for the StopCallback
 */
enum class StopReason {
    NORMAL_TERMINATION,    // stop() was called normally
    STREAM_END,           // End of stream reached
    STREAM_ERROR,         // Stream read error
    INITIALIZATION_ERROR, // Failed to initialize acquisition
    PROCESSING_ERROR,     // Error during processing
    WORKER_EXCEPTION      // Unhandled exception in worker thread
};

/**
 * Manages the complete IQ-to-video demodulation process for a single stream.
 * Takes an initialized IIQStream and orchestrates video processing.
 */
class StreamProcessor {
public:
    using FrameCallback = std::function<void(const uint8_t* frameData, size_t frameSize)>;
    using StopCallback = std::function<void(StopReason reason, const std::string& message)>;

    /**
     * Constructor
     * @param stream Initialized and validated IIQStream (takes ownership)
     * @param config Video processing configuration
     * @param frameCallback Callback for processed video frames (Called when a complete video frame is ready)
     * @param stopCallback Callback for handling stop events (Called on errors, normal termination, or stream end)
     */
    StreamProcessor(std::unique_ptr<IIQStream> stream, ProcessingConfig config, const FrameCallback& frameCallback, const StopCallback& stopCallback);
    ~StreamProcessor();
    // Disable copy and assignment
    StreamProcessor(const StreamProcessor&) = delete;
    StreamProcessor& operator=(const StreamProcessor&) = delete;

    void start(); // Start processing asynchronously
    void stop(); // Stop processing and clean up resources
    bool isRunning() const; // Check if the processor is currently running

private:
    // Configuration and callback
    ProcessingConfig config_;
    const FrameCallback& frameCallback_;
    const StopCallback& stopCallback_;

    // Processing components
    const std::unique_ptr<IIQStream> stream_;

    // Worker thread and state
    std::unique_ptr<std::thread> workerThread_;
    void workerThreadFunc();

    // State management
    std::atomic<bool> running_;
    std::atomic<bool> stopRequested_;

    std::unique_ptr<ChunkProcessor<SampleType>> _createChunkProcessor(ChunkProcessor<SampleType>::ReadHandler &callback) const;
};

} // namespace IQVideoStream
