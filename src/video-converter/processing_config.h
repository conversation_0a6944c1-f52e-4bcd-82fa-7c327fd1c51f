#pragma once
#include <string>


namespace IQVideoStream {

struct ProcessingConfig {
  double centerOffsetHz;      // Frequency offset for demodulation
  std::string sliceStrategy;  // Slice strategy (e.g., "auto_ntsc")
  uint32_t frameWidthPx;      // Output frame width in pixels

  struct QueueDepth {
    uint32_t raw;           // Raw sample queue depth
    uint32_t demod;         // Demodulated data queue depth
    uint32_t lines;         // Video line queue depth
  } queueDepth;

  /**
   * Validate video processing configuration
   * @param errorMsg Output parameter for error message
   * @return true if valid, false otherwise
   */
  [[nodiscard]] bool isValid(std::string &errorMsg) const;
};

inline bool ProcessingConfig::isValid(std::string &errorMsg) const {
  if (frameWidthPx == 0) {
    errorMsg = "Frame width cannot be zero";
    return false;
  }

  if (frameWidthPx > 4096) {
    errorMsg = "Frame width too large (max 4096 pixels)";
    return false;
  }

  if (queueDepth.raw == 0 || queueDepth.demod == 0 || queueDepth.lines == 0) {
    errorMsg = "Queue depths must be positive";
    return false;
  }

  if (queueDepth.raw > 1024 || queueDepth.demod > 1024 || queueDepth.lines > 1024) {
    errorMsg = "Queue depths too large (max 1024 each)";
    return false;
  }

  if (sliceStrategy.empty()) {
    errorMsg = "Slice strategy cannot be empty";
    return false;
  }

  return true;
}


} // namespace IQVideoStream
