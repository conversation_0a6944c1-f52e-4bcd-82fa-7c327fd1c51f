#include "iq_video_stream_processor.h"
#include <stdexcept>
#include <algorithm>
#include <utility>
#include "../chunk-processor/chunk_processor.h"

namespace IQVideoStream {

constexpr size_t STREAM_READ_CHUNK_SIZE = 8 * 1024; // 8K samples

StreamProcessor::StreamProcessor(std::unique_ptr<IIQStream> stream, ProcessingConfig config, const FrameCallback& frameCallback, const StopCallback& stopCallback)
    : config_(std::move(config))
    , frameCallback_(frameCallback)
    , stopCallback_(stopCallback)
    , stream_(std::move(stream))
    , running_(false)
    , stopRequested_(false) {

}

StreamProcessor::~StreamProcessor() {
    stop();
}

void StreamProcessor::start() {
    if (running_.exchange(true)) {
        return;
    }

    workerThread_ = std::make_unique<std::thread>([this]() {
        try {
            workerThreadFunc();
        } catch (const std::exception& e) {
            running_ = false;
            stopCallback_(StopReason::WORKER_EXCEPTION, "Worker thread exception: " + std::string(e.what()));
        }
    });
}

void StreamProcessor::stop() {
    if (running_.exchange(false)) {
        stopRequested_ = true;

        // Wait for worker thread to finish
        if (workerThread_ && workerThread_->joinable()) {
            workerThread_->join();
        }
        workerThread_.reset();

        // Call callback to indicate normal termination
        stopCallback_(StopReason::NORMAL_TERMINATION, "Stream processor stopped normally");
    }
}

bool StreamProcessor::isRunning() const {
    return running_.load();
}

void StreamProcessor::workerThreadFunc() {

    // auto chunkProcessor = std::make_unique<ChunkProcessor>(config_, frameCallback_);

    // // Initialize the acquisition components
    // if (!initializeAcquisition()) {
    //     running_ = false;
    //     stopCallback_(StopReason::INITIALIZATION_ERROR, "Failed to initialize acquisition: " + lastError_);
    //     return;
    // }
    //
    // // Main processing loop
    // while (!stopRequested_ && canContinue()) {
    //     if (!processIteration()) {
    //         // Check if it's a stream error or end of stream
    //         if (stream_ && stream_->isActive()) {
    //             // Stream error
    //             running_ = false;
    //             stopCallback_(StopReason::STREAM_ERROR, "Stream read error: " + lastError_);
    //             return;
    //         } else {
    //             // End of stream
    //             running_ = false;
    //             stopCallback_(StopReason::STREAM_END, "End of stream reached");
    //             return;
    //         }
    //     }
    // }
    //
    // // Close stream when done
    // if (stream_) {
    //     stream_->close();
    // }
    //
    // // If we exit the loop due to stop request, the stop() method will handle the callback
    // running_ = false;
}

std::unique_ptr<ChunkProcessor<SampleType>> StreamProcessor::_createChunkProcessor(ChunkProcessor<SampleType>::ReadHandler &callback) const {
    auto sampleRate = stream_->sampleRate(); // In Hz



    // Create a chunk processor with the configured parameters
    return std::make_unique<ChunkProcessor<SampleType>>(
        STREAM_READ_CHUNK_SIZE,
        0, //config_.readChunkSize,
        0, //config_.readOverlapSize,
        0, //config_.numWriteChunks,
        callback
    );
}

} // namespace IQVideoStream
