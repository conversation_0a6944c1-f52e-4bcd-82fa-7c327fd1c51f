#include "iq_stream_factory.h"
#include "../wav-stream/wav_stream.h"
#include "../bladerf-stream/bladerf_stream.h"
#include <stdexcept>
#include <fstream>

// Static member initialization
std::string IQStreamFactory::lastError_;

std::unique_ptr<IIQStream> IQStreamFactory::createWavStream(const WavStreamConfig& config) {
    try {
        std::string errorMsg;
        if (!validateWavConfig(config, errorMsg)) {
            setError("WAV configuration validation failed: " + errorMsg);
            return nullptr;
        }
        
        bool enableLoop = (config.playMode == "realtime");
        auto stream = std::make_unique<WAVIQStream>(config.filePath, enableLoop);

        // Open the WAV file
        if (!stream->open()) {
            setError("WAV stream failed to open: " + stream->lastError());
            return nullptr;
        }

        if (!stream->isActive()) {
            setError("WAV stream initialization failed: " + stream->lastError());
            return nullptr;
        }
        
        return stream;
        
    } catch (const std::exception& e) {
        setError("Exception creating WAV stream: " + std::string(e.what()));
        return nullptr;
    }
}

std::unique_ptr<IIQStream> IQStreamFactory::createBladeRFStream(const BladeRFStreamConfig& config) {
    try {
        std::string errorMsg;
        if (!validateBladeRFConfig(config, errorMsg)) {
            setError("BladeRF configuration validation failed: " + errorMsg);
            return nullptr;
        }
        
        // Create BladeRF config from the factory config
        BladeRFIQStream::Config bladeRFConfig(
            config.centerHz,     // frequency
            config.sampleRate,   // sampleRate
            config.bandwidth,    // bandwidth
            30                   // gain (default)
        );

        auto stream = std::make_unique<BladeRFIQStream>(
            bladeRFConfig,
            config.serial        // device identifier
        );
        
        if (!stream->isActive()) {
            setError("BladeRF stream initialization failed: " + stream->lastError());
            return nullptr;
        }
        
        return stream;
        
    } catch (const std::exception& e) {
        setError("Exception creating BladeRF stream: " + std::string(e.what()));
        return nullptr;
    }
}

std::unique_ptr<IIQStream> IQStreamFactory::createStream(const StreamConfig& config) {
    try {
        std::string errorMsg;
        if (!validateStreamConfig(config, errorMsg)) {
            setError("Stream configuration validation failed: " + errorMsg);
            return nullptr;
        }
        
        if (config.type == "wav") {
            return createWavStream(config.wav);
        } else if (config.type == "bladerf") {
            return createBladeRFStream(config.bladerf);
        } else {
            setError("Unknown stream type: " + config.type);
            return nullptr;
        }
        
    } catch (const std::exception& e) {
        setError("Exception creating stream: " + std::string(e.what()));
        return nullptr;
    }
}

bool IQStreamFactory::validateWavConfig(const WavStreamConfig& config, std::string& errorMsg) {
    if (config.filePath.empty()) {
        errorMsg = "WAV file path cannot be empty";
        return false;
    }
    
    // Check if file exists and is readable
    std::ifstream file(config.filePath);
    if (!file.good()) {
        errorMsg = "WAV file does not exist or is not readable: " + config.filePath;
        return false;
    }
    
    if (config.playMode != "realtime" && config.playMode != "max") {
        errorMsg = "WAV play mode must be 'realtime' or 'max', got: " + config.playMode;
        return false;
    }
    
    return true;
}

bool IQStreamFactory::validateBladeRFConfig(const BladeRFStreamConfig& config, std::string& errorMsg) {
    if (config.sampleRate == 0) {
        errorMsg = "BladeRF sample rate cannot be zero";
        return false;
    }
    
    if (config.centerHz <= 0) {
        errorMsg = "BladeRF center frequency must be positive";
        return false;
    }
    
    if (config.bandwidth <= 0) {
        errorMsg = "BladeRF bandwidth must be positive";
        return false;
    }
    
    // Validate reasonable ranges
    if (config.sampleRate < 1000000 || config.sampleRate > 61440000) {
        errorMsg = "BladeRF sample rate out of range (1MHz - 61.44MHz)";
        return false;
    }
    
    if (config.centerHz < 47000000.0 || config.centerHz > 6000000000.0) {
        errorMsg = "BladeRF center frequency out of range (47MHz - 6GHz)";
        return false;
    }
    
    return true;
}

bool IQStreamFactory::validateStreamConfig(const StreamConfig& config, std::string& errorMsg) {
    if (config.type.empty()) {
        errorMsg = "Stream type cannot be empty";
        return false;
    }
    
    if (config.type == "wav") {
        return validateWavConfig(config.wav, errorMsg);
    } else if (config.type == "bladerf") {
        return validateBladeRFConfig(config.bladerf, errorMsg);
    } else {
        errorMsg = "Unknown stream type: " + config.type + " (supported: wav, bladerf)";
        return false;
    }
}

const std::string& IQStreamFactory::lastError() {
    return lastError_;
}

void IQStreamFactory::setError(const std::string& error) {
    lastError_ = error;
}
