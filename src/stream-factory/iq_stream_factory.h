#ifndef IQ_STREAM_FACTORY_H
#define IQ_STREAM_FACTORY_H

#include "../iiq-stream/iiq_stream.h"
#include "../types.h"
#include <memory>
#include <string>

/**
 * IQStreamFactory - Factory for creating IQ stream instances
 * 
 * Handles creation and validation of different IQ stream types (WAV, BladeRF)
 * with proper error handling and configuration validation.
 * Separates stream creation concerns from video processing logic.
 */
class IQStreamFactory {
public:
    /**
     * Configuration for WAV file streams
     */
    struct WavStreamConfig {
        std::string filePath;
        std::string playMode;  // "realtime" or "max"
    };
    
    /**
     * Configuration for BladeRF hardware streams
     */
    struct BladeRFStreamConfig {
        std::string serial;     // Device serial (empty for any device)
        uint32_t channel;       // RX channel number
        SampleRateType sampleRate;    // Sample rate in Hz
        double centerHz;        // Center frequency in Hz
        double bandwidth;       // Bandwidth in Hz
    };
    
    /**
     * Unified stream configuration
     */
    struct StreamConfig {
        std::string type;  // "wav" or "bladerf"
        
        // Union-like access to specific configs
        WavStreamConfig wav;
        BladeRFStreamConfig bladerf;
    };
    
    /**
     * Create a WAV file stream
     * @param config WAV stream configuration
     * @return Initialized IIQStream or nullptr on failure
     */
    static std::unique_ptr<IIQStream> createWavStream(const WavStreamConfig& config);
    
    /**
     * Create a BladeRF hardware stream
     * @param config BladeRF stream configuration
     * @return Initialized IIQStream or nullptr on failure
     */
    static std::unique_ptr<IIQStream> createBladeRFStream(const BladeRFStreamConfig& config);
    
    /**
     * Create a stream from unified configuration
     * @param config Stream configuration
     * @return Initialized IIQStream or nullptr on failure
     */
    static std::unique_ptr<IIQStream> createStream(const StreamConfig& config);
    
    /**
     * Validate WAV stream configuration
     * @param config Configuration to validate
     * @param errorMsg Output parameter for error message
     * @return true if valid, false otherwise
     */
    static bool validateWavConfig(const WavStreamConfig& config, std::string& errorMsg);
    
    /**
     * Validate BladeRF stream configuration
     * @param config Configuration to validate
     * @param errorMsg Output parameter for error message
     * @return true if valid, false otherwise
     */
    static bool validateBladeRFConfig(const BladeRFStreamConfig& config, std::string& errorMsg);
    
    /**
     * Validate unified stream configuration
     * @param config Configuration to validate
     * @param errorMsg Output parameter for error message
     * @return true if valid, false otherwise
     */
    static bool validateStreamConfig(const StreamConfig& config, std::string& errorMsg);
    
    /**
     * Get the last error message from factory operations
     * @return Last error message or empty string if no error
     */
    static const std::string& lastError();

private:
    static std::string lastError_;
    
    /**
     * Set error message
     * @param error Error message to set
     */
    static void setError(const std::string& error);
};

#endif // IQ_STREAM_FACTORY_H
