# Stream Processing Pipeline Framework

A composable, tickable stream processing framework for real-time data processing with zero-copy semantics and robust pipeline monitoring.

## Overview

The Stream Processing Pipeline Framework provides a foundation for building high-performance, real-time data processing pipelines with enhanced monitoring and graceful shutdown capabilities. It consists of three main components:

1. **Tickable** - Base interface for components that can be polled for processing with shutdown support
2. **StreamLink** - Active components that manage data flow between pipeline stages with failure detection
3. **StreamNode** - Passive components that process data when called

## New Features (Enhanced Monitoring System)

- **Robust Pipeline Monitoring**: Top-level monitoring of each `.tick()` return value for pipeline health
- **Graceful Shutdown**: Enhanced shutdown mechanisms with proper cleanup and thread synchronization
- **Failure Detection**: Automatic pipeline termination on component failures or buffer overflows
- **Timeout Handling**: Configurable timeouts for blocking operations with proper timeout behavior
- **Buffer Management**: Non-blocking forward operations with overflow detection and handling

## Design Principles

- **Zero-copy semantics**: All data transfer uses move semantics (&&) to avoid unnecessary copies
- **Passive nodes**: StreamNode instances only process when their process() method is called
- **Active links**: StreamLink instances manage data flow and may implement custom tick() behavior
- **Ownership transfer**: Rvalue references indicate clear ownership transfer at each stage
- **Composability**: Components can be chained together through link connections

## Components

### Tickable Interface

Base interface for components that need periodic processing with enhanced shutdown support:

```cpp
class Tickable {
public:
    virtual ~Tickable() = default;
    virtual bool tick() = 0;  // Returns true to continue, false to stop (shutdown/error)
    virtual bool hasPendingWork() const { return false; }  // Optional override
    virtual void shutdown() {}  // Initiate graceful shutdown
};
```

**Enhanced Behavior:**
- `tick()` now returns `false` to signal pipeline termination (shutdown or error)
- `shutdown()` method enables graceful component shutdown
- Thread-safe shutdown coordination across pipeline components

### StreamLink Template

Template-based interface for connecting stream processing components:

```cpp
template<typename TInput, typename TOutput>
class StreamLink : public Tickable {
public:
    using OutputCallback = std::function<void(TOutput&&)>;
    
    virtual void forward(TInput&& data) = 0;
    virtual void onOutput(OutputCallback callback) = 0;
};
```

### StreamNode Template

Abstract base class for passive stream processing components:

```cpp
template<typename TInput, typename TOutput>
class StreamNode {
public:
    template<typename TLinkOutput>
    void connectInputLink(StreamLink<TLinkOutput, TInput>* link);

    template<typename TLinkInput>
    void connectOutputLink(StreamLink<TOutput, TLinkInput>* link);

protected:
    virtual bool process(TInput&& input) = 0;
    void sendOutput(TOutput&& data);
};
```

## Pipeline Monitoring System

The enhanced framework includes a robust pipeline monitoring system that enables:

### Key Features

1. **Return Value Monitoring**: Each `tick()` call returns a boolean indicating whether to continue processing
2. **Automatic Termination**: Pipeline stops when any component returns `false` from `tick()`
3. **Graceful Shutdown**: Components can be shut down individually or collectively
4. **Failure Propagation**: Errors in one component trigger pipeline-wide shutdown

### Basic Monitoring Pattern

```cpp
// Simple monitoring loop
bool pipelineRunning = true;
while (pipelineRunning) {
    for (auto* component : pipelineComponents) {
        bool shouldContinue = component->tick();
        if (!shouldContinue) {
            // Component signaled to stop - initiate shutdown
            for (auto* comp : pipelineComponents) {
                comp->shutdown();
            }
            pipelineRunning = false;
            break;
        }
    }
}
```

### Advanced Pipeline Monitor

```cpp
class PipelineMonitor {
    std::vector<Tickable*> components_;
    std::atomic<bool> isRunning_{false};

public:
    void addComponent(Tickable* component) {
        components_.push_back(component);
    }

    bool runSingleCycle() {
        for (auto* component : components_) {
            if (!component->tick()) {
                terminate(); // Shutdown all components
                return false;
            }
        }
        return true;
    }

    void terminate() {
        for (auto* component : components_) {
            component->shutdown();
        }
        isRunning_ = false;
    }
};
```

## Usage Example

```cpp
#include "stream-pipeline/tickable.h"
#include "stream-pipeline/stream_link.h"
#include "stream-pipeline/stream_node.h"

using namespace SPipeline;

// Example data types
struct InputData { int value; };
struct ProcessedData { int processed_value; };

// Example processing node
class MyProcessor : public StreamNode<InputData, ProcessedData> {
protected:
    bool process(InputData&& input) override {
        ProcessedData result;
        result.processed_value = input.value * 2;  // Simple processing
        return sendOutput(std::move(result));
    }
};

// Example link implementation
class MyLink : public StreamLink<ProcessedData, ProcessedData> {
private:
    OutputCallback outputCallback_;
    
public:
    void forward(ProcessedData&& data) override {
        // Pass through or transform data
        if (outputCallback_) {
            outputCallback_(std::move(data));
        }
    }
    
    void onOutput(OutputCallback callback) override {
        outputCallback_ = std::move(callback);
    }
    
    bool tick() override {
        // Perform any periodic processing
        return false;  // No pending work
    }
};

// Usage
MyProcessor processor;
MyLink link;
PassthroughLink<InputData> inputLink;

// Connect components
inputLink.connectOutputLink(&processor);
processor.connectOutputLink(&link);

// Process data through the pipeline
InputData input{42};
inputLink.forward(std::move(input));
```

## Integration with Existing Project

The framework is designed to integrate seamlessly with the existing BladeRF video project:

- Uses the same header guard style (`#ifndef` instead of `#pragma once`)
- Follows existing template and documentation patterns
- Compatible with existing `SampleType` definitions from `types.h`
- Designed for future extension with video decoders, stream converters, etc.

## Concrete Implementations

The framework includes two ready-to-use link implementations:

### PassthroughLink
A synchronous, zero-copy link for same-thread data forwarding:
```cpp
PassthroughLink<MyDataType> link;
link.onOutput([](MyDataType&& data) { /* process data */ });
link.forward(std::move(data));  // Immediate forwarding
```

### AsyncBridge (Enhanced)
A thread-safe buffering link for cross-thread communication with robust monitoring:
```cpp
// Enhanced constructor with timeout support
AsyncBridge<MyDataType> bridge(100, std::chrono::milliseconds(50));

// Non-blocking forward with return value
bool success = bridge.forward(std::move(data));
if (!success) {
    // Buffer full or shutting down
    handleBackpressure();
}

// Callback with failure detection
bridge.onOutput([](MyDataType&& data) -> bool {
    bool success = processData(data);
    return success; // Return false to trigger pipeline shutdown
});

// Enhanced tick with timeout and failure detection
bool shouldContinue = bridge.tick();
if (!shouldContinue) {
    // Timeout during shutdown or callback failure
    initiatePipelineShutdown();
}
```

**New AsyncBridge Features:**
- **Non-blocking forward()**: Returns `false` if buffer full or shutting down
- **Configurable timeouts**: Constructor accepts timeout parameter for `tick()` blocking
- **Callback failure detection**: Output callbacks can signal errors by returning `false`
- **Enhanced shutdown**: Proper thread synchronization and cleanup

See `LINK_IMPLEMENTATIONS.md` for detailed documentation and `examples/` for usage examples.

## Future Extensions

This framework provides the foundation for implementing:

- Video stream decoders
- IQ data processors
- Real-time filters and converters
- Multi-stage processing pipelines
- Hardware-accelerated processing components

## Thread Safety

The framework provides both thread-safe and non-thread-safe components:

### Thread-Safe Components
- **AsyncBridge**: Fully thread-safe for cross-thread communication
- Uses mutex and condition variables for safe buffering
- Prevents deadlocks through careful lock management

### Non-Thread-Safe Components
- **PassthroughLink**: Designed for same-thread use only
- **StreamNode**: Base class requires external synchronization

### Multi-threaded Usage Guidelines
- Use PassthroughLink for same-thread connections
- Use AsyncBridge for cross-thread connections
- Ensure proper ownership transfer across thread boundaries
- Consider tick() frequency for AsyncBridge performance
