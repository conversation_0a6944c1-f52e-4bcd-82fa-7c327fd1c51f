# Build artifacts
*.o
*.obj
*.so
*.dylib
*.dll
*.a
*.lib

# Test executables
stream_pipeline_tests
stream_pipeline_tests.exe

# Build directory
build/

# Temporary build files
*.tmp
*.temp
*~
.DS_Store

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*.bak

# Profiling and debugging files
*.gcda
*.gcno
*.gcov
gmon.out
callgrind.out.*
massif.out.*

# Static analysis output
*.plist

# Backup files
*.orig
*.rej

# Log files
*.log

# Core dumps
core
core.*

# Memory check output
valgrind-*.log
valgrind-*.xml

# Performance test output files
perf_results_*.txt
benchmark_*.csv
