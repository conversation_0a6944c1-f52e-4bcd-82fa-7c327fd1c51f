CXX = g++
CXXFLAGS = -std=c++17 -I.. -I../.. -pthread -Wall -Wextra
BUILDDIR = build

# Source files
SOURCES = basic_pipeline.cpp simple_test.cpp
TARGETS = $(SOURCES:.cpp=)

# Default target
all: $(BUILDDIR) $(addprefix $(BUILDDIR)/, $(TARGETS))

# Create build directory
$(BUILDDIR):
	mkdir -p $(BUILDDIR)

# Build individual targets
$(BUILDDIR)/%: %.cpp | $(BUILDDIR)
	$(CXX) $(CXXFLAGS) $< -o $@

# Run tests
test: $(BUILDDIR)/simple_test
	@echo "Running simple test..."
	@$(BUILDDIR)/simple_test

# Run basic pipeline example
run-basic: $(BUILDDIR)/basic_pipeline
	@echo "Running basic pipeline example..."
	@$(BUILDDIR)/basic_pipeline

# Clean build artifacts
clean:
	rm -rf $(BUILDDIR)

.PHONY: all test run-basic clean
