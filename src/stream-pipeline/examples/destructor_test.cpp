#include "../async_bridge.h"
#include "../passthrough_link.h"
#include "../pipeline_component.h"
#include "../stream_link.h"
#include "../stream_node.h"
#include <iostream>
#include <memory>

using namespace SPipeline;

// Test data type
struct TestData {
    int value;
    TestData(int v = 0) : value(v) {}
};

// Test node implementation
class TestNode : public StreamNode<TestData, TestData> {
protected:
    bool process(TestData&& input) override {
        return sendOutput(std::move(input));
    }
};

int main() {
    std::cout << "=== Virtual Destructor Test ===" << std::endl;
    
    // Test that all classes can be instantiated and destroyed properly
    {
        std::cout << "Testing PassthroughLink..." << std::endl;
        auto link = std::make_unique<PassthroughLink<TestData>>();
        // Should destroy cleanly
    }
    
    {
        std::cout << "Testing AsyncBridge..." << std::endl;
        auto bridge = std::make_unique<AsyncBridge<TestData>>();
        // Should destroy cleanly
    }
    
    {
        std::cout << "Testing TestNode..." << std::endl;
        auto node = std::make_unique<TestNode>();
        // Should destroy cleanly
    }
    
    // Test polymorphic destruction
    {
        std::cout << "Testing polymorphic destruction..." << std::endl;
        std::unique_ptr<PipelineComponent> tickable1 = std::make_unique<PassthroughLink<TestData>>();
        std::unique_ptr<PipelineComponent> tickable2 = std::make_unique<AsyncBridge<TestData>>();
        std::unique_ptr<PipelineComponent> tickable3 = std::make_unique<TestNode>();
        
        std::unique_ptr<IStreamLinkInput<TestData>> input1 = std::make_unique<PassthroughLink<TestData>>();
        std::unique_ptr<IStreamLinkInput<TestData>> input2 = std::make_unique<AsyncBridge<TestData>>();
        
        std::unique_ptr<IStreamLinkOutput<TestData>> output1 = std::make_unique<PassthroughLink<TestData>>();
        std::unique_ptr<IStreamLinkOutput<TestData>> output2 = std::make_unique<AsyncBridge<TestData>>();
        
        // All should destroy cleanly through base class pointers
    }
    
    std::cout << "All destructor tests PASSED!" << std::endl;
    return 0;
}
