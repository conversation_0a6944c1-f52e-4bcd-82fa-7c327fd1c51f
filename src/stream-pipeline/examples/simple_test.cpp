#include "../passthrough_link.h"
#include "../async_bridge.h"
#include <iostream>
#include <thread>

using namespace SPipeline;

struct TestData {
    int value;
    TestData(int v = 0) : value(v) {}
};

int main() {
    std::cout << "=== Simple Stream Pipeline Test ===" << std::endl;
    
    // Test PassthroughLink
    std::cout << "\n--- Testing PassthroughLink ---" << std::endl;
    PassthroughLink<TestData> passthrough;
    
    bool received = false;
    passthrough.onOutput([&received](TestData&& data) -> bool {
        std::cout << "PassthroughLink received: " << data.value << std::endl;
        received = true;
        return true;
    });
    
    TestData testData(42);
    passthrough.forward(std::move(testData));
    
    std::cout << "PassthroughLink test " << (received ? "PASSED" : "FAILED") << std::endl;
    
    // Test AsyncBridge
    std::cout << "\n--- Testing AsyncBridge ---" << std::endl;
    AsyncBridge<TestData> bridge(10);
    
    int receivedCount = 0;
    bridge.onOutput([&receivedCount](TestData&& data) -> bool {
        std::cout << "AsyncBridge received: " << data.value << std::endl;
        receivedCount++;
        return true;
    });
    
    // Add some data
    for (int i = 0; i < 5; ++i) {
        TestData data(i);
        bridge.forward(std::move(data));
    }
    
    std::cout << "Buffer size after adding 5 items: " << bridge.size() << std::endl;
    
    // Process the data
    while (bridge.hasPendingWork()) {
        bridge.tick();
    }
    
    std::cout << "Buffer size after processing: " << bridge.size() << std::endl;
    std::cout << "Received count: " << receivedCount << std::endl;
    std::cout << "AsyncBridge test " << (receivedCount == 5 ? "PASSED" : "FAILED") << std::endl;
    
    // Test shutdown
    bridge.stop();
    std::cout << "AsyncBridge shutdown completed" << std::endl;
    
    return 0;
}
