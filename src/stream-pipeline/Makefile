# Makefile for Stream Pipeline Framework Tests
# BladeRF Video Decoding Project - Stream Pipeline Module

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -pthread
INCLUDES = -I. -I..
TEST_DIR = tests
BUILD_DIR = build

# Test source files
TEST_SOURCES = $(TEST_DIR)/test_runner.cpp \
               $(TEST_DIR)/test_async_bridge.cpp \
               $(TEST_DIR)/test_tickable_interface.cpp \
               $(TEST_DIR)/test_pipeline_monitoring.cpp

# Test executable
TEST_EXECUTABLE = $(BUILD_DIR)/stream_pipeline_tests

# Header files for dependency tracking
HEADERS = pipeline_component.h \
          stream_link.h \
          stream_node.h \
          async_bridge.h \
          passthrough_link.h

.PHONY: all clean build test verify perf validate-header help

# Default target
all: build

# Help target
help:
	@echo "Stream Pipeline Framework Test Makefile"
	@echo "========================================"
	@echo ""
	@echo "Targets:"
	@echo "  build           Build test executable"
	@echo "  test            Run all tests (default output)"
	@echo "  verify          Run quick verification tests"
	@echo "  perf            Run performance benchmarks only"
	@echo "  validate-header Validate header-only implementation"
	@echo "  clean           Clean build artifacts"
	@echo "  help            Show this help message"
	@echo ""
	@echo "Examples:"
	@echo "  make build      # Build tests"
	@echo "  make test       # Run all tests"
	@echo "  make verify     # Quick verification"
	@echo "  make perf       # Performance tests only"

# Create build directory
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

# Validate header-only implementation
validate-header: $(HEADERS)
	@echo "Validating header-only implementation..."
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only pipeline_component.h
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only stream_link.h
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only stream_node.h
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only async_bridge.h
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only passthrough_link.h
	@echo "Header validation completed successfully."

# Build test executable
build: validate-header $(TEST_EXECUTABLE)

$(TEST_EXECUTABLE): $(TEST_SOURCES) $(HEADERS) | $(BUILD_DIR)
	@echo "Building Stream Pipeline tests..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(TEST_SOURCES) -o $(TEST_EXECUTABLE)
	@echo "Build completed: $(TEST_EXECUTABLE)"

# Run all tests
test: $(TEST_EXECUTABLE)
	@echo "Running Stream Pipeline tests..."
	./$(TEST_EXECUTABLE)

# Run quick verification tests
verify: $(TEST_EXECUTABLE)
	@echo "Running quick verification tests..."
	./$(TEST_EXECUTABLE) --quick

# Run performance benchmarks only
perf: $(TEST_EXECUTABLE)
	@echo "Running performance benchmarks..."
	./$(TEST_EXECUTABLE) --perf

# Clean build artifacts
clean:
	@echo "Cleaning Stream Pipeline build artifacts..."
	@if [ -d "$(BUILD_DIR)" ]; then rm -f $(BUILD_DIR)/*; fi
	@echo "Clean completed."

# Dependencies
$(TEST_DIR)/test_runner.cpp: $(TEST_DIR)/test_async_bridge.cpp \
                            $(TEST_DIR)/test_tickable_interface.cpp \
                            $(TEST_DIR)/test_pipeline_monitoring.cpp

$(TEST_DIR)/test_async_bridge.cpp: async_bridge.h stream_link.h pipeline_component.h

$(TEST_DIR)/test_tickable_interface.cpp: pipeline_component.h passthrough_link.h stream_link.h

$(TEST_DIR)/test_pipeline_monitoring.cpp: async_bridge.h passthrough_link.h \
                                         stream_node.h stream_link.h pipeline_component.h
