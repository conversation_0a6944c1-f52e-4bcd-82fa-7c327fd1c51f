#!/bin/bash

# Test runner script for Stream Pipeline Framework
# BladeRF Video Decoding Project - Stream Pipeline Module

set -e  # Exit on any error

echo "Stream Pipeline Framework Test Runner"
echo "====================================="
echo "Enhanced pipeline monitoring and shutdown system tests"
echo ""

# Check if we're in the right directory
if [ ! -f "stream_link.h" ] || [ ! -f "stream_node.h" ]; then
    echo "Error: Must be run from src/stream-pipeline directory"
    echo "Usage: cd src/stream-pipeline && ./run_tests.sh"
    exit 1
fi

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "\033[34m[INFO]\033[0m $message"
            ;;
        "SUCCESS")
            echo -e "\033[32m[SUCCESS]\033[0m $message"
            ;;
        "ERROR")
            echo -e "\033[31m[ERROR]\033[0m $message"
            ;;
        "WARNING")
            echo -e "\033[33m[WARNING]\033[0m $message"
            ;;
    esac
}

# Parse command line arguments
QUICK_MODE=false
VERBOSE_MODE=false
PERFORMANCE_ONLY=false
HELP_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE_MODE=true
            shift
            ;;
        -p|--perf)
            PERFORMANCE_ONLY=true
            shift
            ;;
        -h|--help)
            HELP_MODE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            HELP_MODE=true
            shift
            ;;
    esac
done

# Show help if requested
if [ "$HELP_MODE" = true ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -q, --quick    Quick verification mode (limited output)"
    echo "  -v, --verbose  Verbose mode (full output)"
    echo "  -p, --perf     Performance benchmarks only"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run all tests with standard output"
    echo "  $0 --quick      # Quick verification"
    echo "  $0 --verbose    # Full verbose output"
    echo "  $0 --perf       # Performance tests only"
    echo ""
    exit 0
fi

# Clean previous builds
print_status "INFO" "Cleaning previous builds..."
make clean > /dev/null 2>&1

# Validate header-only implementation first
print_status "INFO" "Validating header-only implementation..."
if make validate-header > /dev/null 2>&1; then
    print_status "SUCCESS" "Header validation passed"
else
    print_status "ERROR" "Header validation failed"
    exit 1
fi

# Build tests
print_status "INFO" "Building Stream Pipeline tests..."
if make build > /dev/null 2>&1; then
    print_status "SUCCESS" "Build completed successfully"
else
    print_status "ERROR" "Build failed"
    exit 1
fi

# Run tests based on mode
if [ "$PERFORMANCE_ONLY" = true ]; then
    print_status "INFO" "Running performance benchmarks only..."
    echo ""
    make perf
elif [ "$QUICK_MODE" = true ]; then
    print_status "INFO" "Running quick verification..."
    echo ""
    make verify
elif [ "$VERBOSE_MODE" = true ]; then
    print_status "INFO" "Running all tests in verbose mode..."
    echo ""
    make test
else
    print_status "INFO" "Running all tests with standard output..."
    echo ""
    make test
fi

# Capture exit code
TEST_EXIT_CODE=$?

echo ""
echo "====================================="

if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_status "SUCCESS" "All Stream Pipeline tests completed successfully!"
    echo ""
    echo "Key Features Verified:"
    echo "  ✓ Enhanced AsyncBridge with robust monitoring"
    echo "  ✓ Improved Tickable interface with shutdown support"
    echo "  ✓ Pipeline monitoring and failure detection"
    echo "  ✓ Graceful shutdown mechanisms"
    echo "  ✓ Buffer overflow and timeout handling"
    echo "  ✓ Multi-component pipeline coordination"
    echo "  ✓ Thread-safe cross-thread communication"
    echo "  ✓ Comprehensive error handling and recovery"
    echo ""
    echo "Next Steps:"
    echo "  1. Integrate with main project build system"
    echo "  2. Update existing pipeline implementations"
    echo "  3. Test with BladeRF hardware integration"
    echo "  4. Consider renaming Tickable to PipelineComponent"
    echo ""
else
    print_status "ERROR" "Some tests failed (exit code: $TEST_EXIT_CODE)"
    echo ""
    echo "Troubleshooting:"
    echo "  - Review test output above for specific failures"
    echo "  - Run with --verbose for detailed information"
    echo "  - Check thread safety and timing issues"
    echo "  - Verify shutdown sequence and error handling"
    echo ""
fi

echo "====================================="
exit $TEST_EXIT_CODE
