#pragma once

#include <cstdint>
#include <cstddef>

/**
 * SampleType - Standardized type for IQ samples
 * 
 * Represents a single IQ sample as a 32-bit little-endian word (0xQQQQIIII)
 * where the lower 16 bits are I (in-phase) and upper 16 bits are Q (quadrature).
 */
using SampleType = uint32_t;
using SampleRateType = uint32_t;

/**
 * ComplexType - Standardized type for complex number components
 *
 * Used as the base type for std::complex<ComplexType> in IQ processing.
 * Represents normalized floating-point values in the range [-1.0, 1.0].
 */
using TFloat = float;
