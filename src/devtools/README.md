# Debug Data Visualization System

A comprehensive debugging visualization system for analyzing data arrays in C++ applications. The system consists of a header-only C++ library for data export and a Python script for interactive visualization.

## Overview

This system enables developers to easily export debug data from C++ code and visualize it using Python plots. Data is automatically organized by measurement sessions and grouped for comparison analysis.

**Key Features:**
- Header-only C++ implementation for easy integration
- Template-based design supporting multiple data types
- Automatic file organization and type detection
- Interactive Python visualization with matplotlib
- Real-time monitoring for new data files
- Sequential measurement display with user control

## Components

### 1. C++ Data Exporter (`data_exporter.hpp`)

Header-only template library that exports data arrays to binary files.

**Supported Data Types:**
- `float`, `double`
- `int8_t`, `uint8_t`, `int16_t`, `uint16_t`
- `int32_t`, `uint32_t`, `int64_t`, `uint64_t`

**File Naming Convention:**
```
dev_debug/{prefixName}_{measurementId}_{dataName}_{dataType}.bin
```

### 2. Python Visualizer (`visualizer.py`)

Interactive visualization script that scans for binary files and creates plots grouped by measurement sessions.

## C++ Usage

### Basic Integration

1. Include the header in your C++ code:
```cpp
#include "devtools/data_exporter.hpp"
```

2. Export debug data using the template function:
```cpp
template<typename T>
bool export_debug_data(const std::string& prefixName, 
                      const std::string& dataName,
                      int measurementId, 
                      const T* data, 
                      size_t elements);
```

### Usage Examples

**Example 1: Signal Processing Debug**
```cpp
#include "devtools/data_exporter.hpp"

void processSignal() {
    // Generate test signal
    std::vector<float> input_signal(1000);
    std::vector<float> filtered_signal(1000);
    
    // ... signal processing code ...
    
    // Export debug data for measurement session 42
    DevTools::export_debug_data("filter", "input", 42, 
                                input_signal.data(), input_signal.size());
    DevTools::export_debug_data("filter", "output", 42, 
                                filtered_signal.data(), filtered_signal.size());
}
```

**Example 2: Multiple Data Types**
```cpp
void debugDemodulator() {
    std::vector<uint32_t> raw_samples(2048);
    std::vector<int16_t> i_channel(1024);
    std::vector<int16_t> q_channel(1024);
    std::vector<float> magnitude(1024);
    
    // ... demodulation processing ...
    
    int session_id = 100;
    DevTools::export_debug_data("demod", "raw", session_id, 
                                raw_samples.data(), raw_samples.size());
    DevTools::export_debug_data("demod", "i_channel", session_id, 
                                i_channel.data(), i_channel.size());
    DevTools::export_debug_data("demod", "q_channel", session_id, 
                                q_channel.data(), q_channel.size());
    DevTools::export_debug_data("demod", "magnitude", session_id, 
                                magnitude.data(), magnitude.size());
}
```

**Example 3: Error Handling**
```cpp
bool exportWithErrorHandling() {
    std::vector<double> data = {1.0, 2.0, 3.0, 4.0, 5.0};
    
    if (!DevTools::export_debug_data("test", "sample", 1, 
                                     data.data(), data.size())) {
        std::cerr << "Failed to export debug data!" << std::endl;
        return false;
    }
    
    std::cout << "Debug data exported successfully" << std::endl;
    return true;
}
```

## Python Visualization

### Installation Requirements

This project uses a Python virtual environment to manage dependencies and avoid conflicts with system packages.

**Quick Setup (recommended):**
```bash
# Run the setup script from project root
./setup_debug_visualizer.sh
```

**Manual Setup:**
```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Install required packages
pip install numpy matplotlib
```

**For subsequent use:**
```bash
# Activate virtual environment (required each time)
source venv/bin/activate
```

### Running the Visualizer

**From project root directory:**

**One-time scan and display:**
```bash
source venv/bin/activate && python3 src/devtools/visualizer.py
```

**Continuous monitoring mode:**
```bash
source venv/bin/activate && python3 src/devtools/visualizer.py --watch
```

**Custom debug directory:**
```bash
source venv/bin/activate && python3 src/devtools/visualizer.py --debug-dir /path/to/debug/files
```

**Alternative - from devtools directory:**
```bash
cd src/devtools
source ../../venv/bin/activate
python3 visualizer.py
```

### Visualization Features

- **Automatic Grouping**: Files are grouped by `(prefixName, measurementId)`
- **Sequential Display**: Measurements are shown one at a time, sorted by prefix and ID
- **Interactive Control**: Close current plot window to proceed to next measurement
- **Multi-dataset Plots**: All datasets from the same measurement are plotted together
- **Type-aware Reading**: Binary data is read according to the C++ data type
- **Legend Support**: Each dataset is labeled with name and data type

## Complete Workflow Example

### 1. C++ Code Export
```cpp
// In your signal processing function
void analyzeSignal() {
    std::vector<float> raw_data(1024);
    std::vector<float> filtered_data(1024);
    std::vector<float> fft_magnitude(512);
    
    // ... processing steps ...
    
    // Export all data for measurement session 1
    DevTools::export_debug_data("analysis", "raw", 1, 
                                raw_data.data(), raw_data.size());
    DevTools::export_debug_data("analysis", "filtered", 1, 
                                filtered_data.data(), filtered_data.size());
    DevTools::export_debug_data("analysis", "fft_mag", 1, 
                                fft_magnitude.data(), fft_magnitude.size());
}
```

### 2. Run Your C++ Application
```bash
# Compile and run your application
make build
./your_application
```

### 3. Visualize Results
```bash
# Activate virtual environment and run visualizer from project root
source venv/bin/activate && python3 src/devtools/visualizer.py
```

### 4. Expected Output
```
Scanning dev_debug for debug files...
Found 1 measurement(s)

Found measurement: analysis_1
  Datasets: ['raw', 'filtered', 'fft_mag']
  Plotted raw: 1024 samples (float)
  Plotted filtered: 1024 samples (float)
  Plotted fft_mag: 512 samples (float)
Displaying plot for analysis_1. Close window to continue...
```

## File Organization

```
src/devtools/
├── data_exporter.hpp    # C++ header-only library
├── visualizer.py        # Python visualization script
├── README.md           # This documentation
└── .gitignore          # Excludes debug artifacts

dev_debug/              # Created automatically
├── analysis_1_raw_float.bin
├── analysis_1_filtered_float.bin
└── analysis_1_fft_mag_float.bin
```

## Integration Notes

- **Header-only Design**: No compilation overhead, just include the header
- **Automatic Directory Creation**: `dev_debug/` is created automatically
- **Error Handling**: All functions return boolean success/failure status
- **Thread Safety**: Each export operation is atomic (single file write)
- **Performance**: Minimal overhead, direct binary file writing

## Troubleshooting

**Common Issues:**

1. **Python Dependencies Missing**:
   ```bash
   # Error: ModuleNotFoundError: No module named 'matplotlib'
   # Solution: Set up virtual environment and install dependencies
   python3 -m venv venv
   source venv/bin/activate
   pip install numpy matplotlib
   ```

2. **Virtual Environment Not Activated**:
   ```bash
   # Always activate before running visualizer
   source venv/bin/activate
   ```

3. **Permission Errors**: Ensure write permissions for `dev_debug/` directory

4. **File Not Found**: Check that C++ code successfully exported files

5. **Type Mismatches**: Verify data type names match between C++ and Python

6. **Empty Plots**: Check file sizes and data validity

7. **Externally Managed Environment Error**:
   ```bash
   # If you get "externally-managed-environment" error
   # Use virtual environment instead of system-wide installation
   ```

**Debug Tips:**

- Use `--debug-dir` to specify custom debug directory
- Check console output for export confirmation messages
- Verify file existence in `dev_debug/` directory before running visualizer
- Use `--watch` mode for real-time debugging during development
- Test parsing without GUI: `python3 test_visualizer_parsing.py`

**Virtual Environment Management:**

```bash
# Create virtual environment (one-time setup)
python3 -m venv venv

# Activate (required each session)
source venv/bin/activate

# Deactivate when done
deactivate

# Remove virtual environment (if needed)
rm -rf venv
```
