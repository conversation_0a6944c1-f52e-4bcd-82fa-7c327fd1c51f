#!/usr/bin/env python3
"""
Debug Data Visualizer

Scans dev_debug/ directory for binary data files exported by the C++ DataExporter,
groups them by measurement sessions, and creates interactive plots for analysis.

File naming convention: {prefix}_{measurementId}_{dataName}_{dataType}.bin

Usage:
    python visualizer.py [--watch] [--debug-dir DIR]
    
Options:
    --watch         Continuously monitor for new files
    --debug-dir     Specify debug directory (default: dev_debug)
"""

import os
import re
import sys
import time
import struct
import argparse
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any

class DataTypeHandler:
    """Handles reading binary data based on C++ data types"""
    
    TYPE_MAP = {
        'float': ('f', 4),      # 32-bit float
        'double': ('d', 8),     # 64-bit double
        'int8_t': ('b', 1),     # signed 8-bit
        'uint8_t': ('B', 1),    # unsigned 8-bit
        'int16_t': ('h', 2),    # signed 16-bit
        'uint16_t': ('H', 2),   # unsigned 16-bit
        'int32_t': ('i', 4),    # signed 32-bit
        'uint32_t': ('I', 4),   # unsigned 32-bit
        'int64_t': ('q', 8),    # signed 64-bit
        'uint64_t': ('Q', 8),   # unsigned 64-bit
    }
    
    @classmethod
    def read_binary_file(cls, filepath: Path, data_type: str) -> Optional[np.ndarray]:
        """Read binary file and return numpy array"""
        try:
            if data_type not in cls.TYPE_MAP:
                print(f"Warning: Unknown data type '{data_type}', treating as float")
                data_type = 'float'
            
            format_char, element_size = cls.TYPE_MAP[data_type]
            
            with open(filepath, 'rb') as f:
                data = f.read()
            
            if len(data) % element_size != 0:
                print(f"Warning: File size not aligned to {data_type} boundary: {filepath}")
                # Truncate to aligned size
                data = data[:len(data) - (len(data) % element_size)]
            
            element_count = len(data) // element_size
            if element_count == 0:
                print(f"Warning: Empty or invalid file: {filepath}")
                return None
            
            # Unpack binary data using little-endian format
            format_string = f'<{element_count}{format_char}'
            values = struct.unpack(format_string, data)
            
            return np.array(values)
            
        except Exception as e:
            print(f"Error reading {filepath}: {e}")
            return None

class MeasurementGroup:
    """Represents a group of data files from the same measurement session"""
    
    def __init__(self, prefix: str, measurement_id: int):
        self.prefix = prefix
        self.measurement_id = measurement_id
        self.datasets: Dict[str, Tuple[Path, str]] = {}  # dataName -> (filepath, dataType)
    
    def add_dataset(self, data_name: str, filepath: Path, data_type: str):
        """Add a dataset to this measurement group"""
        self.datasets[data_name] = (filepath, data_type)
    
    def get_key(self) -> Tuple[str, int]:
        """Get sorting key for this measurement"""
        return (self.prefix, self.measurement_id)
    
    def plot(self):
        """Create and display plot for this measurement group"""
        if not self.datasets:
            print(f"No datasets found for {self.prefix}_{self.measurement_id}")
            return
        
        plt.figure(figsize=(12, 8))
        plt.title(f'Measurement: {self.prefix}_{self.measurement_id}')
        
        colors = plt.cm.tab10(np.linspace(0, 1, len(self.datasets)))
        
        for i, (data_name, (filepath, data_type)) in enumerate(self.datasets.items()):
            data = DataTypeHandler.read_binary_file(filepath, data_type)
            if data is not None:
                x_axis = np.arange(len(data))
                plt.plot(x_axis, data, color=colors[i], label=f'{data_name} ({data_type})', linewidth=1.0)
                print(f"  Plotted {data_name}: {len(data)} samples ({data_type})")
        
        plt.xlabel('Sample Index')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        print(f"Displaying plot for {self.prefix}_{self.measurement_id}. Close window to continue...")
        plt.show()

class DebugVisualizer:
    """Main visualizer class that scans and processes debug files"""
    
    def __init__(self, debug_dir: str = "dev_debug"):
        self.debug_dir = Path(debug_dir)
        self.filename_pattern = re.compile(r'^(.+?)_(\d+)_(.+?)_((?:int|uint|float|double)\w*)\.bin$')
        self.processed_files = set()
    
    def scan_files(self) -> Dict[Tuple[str, int], MeasurementGroup]:
        """Scan debug directory and group files by measurement"""
        measurements = {}
        
        if not self.debug_dir.exists():
            return measurements
        
        for filepath in self.debug_dir.glob('*.bin'):
            match = self.filename_pattern.match(filepath.name)
            if not match:
                print(f"Warning: Filename doesn't match pattern: {filepath.name}")
                continue
            
            prefix, measurement_id_str, data_name, data_type = match.groups()
            try:
                measurement_id = int(measurement_id_str)
            except ValueError:
                print(f"Warning: Invalid measurement ID in filename: {filepath.name}")
                continue
            
            key = (prefix, measurement_id)
            if key not in measurements:
                measurements[key] = MeasurementGroup(prefix, measurement_id)
            
            measurements[key].add_dataset(data_name, filepath, data_type)
        
        return measurements
    
    def process_new_measurements(self, measurements: Dict[Tuple[str, int], MeasurementGroup]):
        """Process and display new measurements"""
        new_measurements = []
        
        for key, measurement in measurements.items():
            # Check if this is a new measurement (any new files)
            measurement_files = {str(filepath) for filepath, _ in measurement.datasets.values()}
            if not measurement_files.issubset(self.processed_files):
                new_measurements.append(measurement)
                self.processed_files.update(measurement_files)
        
        # Sort by prefix and measurement ID
        new_measurements.sort(key=lambda m: m.get_key())
        
        for measurement in new_measurements:
            print(f"\nFound measurement: {measurement.prefix}_{measurement.measurement_id}")
            print(f"  Datasets: {list(measurement.datasets.keys())}")
            measurement.plot()
    
    def run_once(self):
        """Scan and process files once"""
        print(f"Scanning {self.debug_dir} for debug files...")
        measurements = self.scan_files()
        
        if not measurements:
            print("No debug files found.")
            return
        
        print(f"Found {len(measurements)} measurement(s)")
        self.process_new_measurements(measurements)
    
    def run_watch(self, interval: float = 1.0):
        """Continuously monitor for new files"""
        print(f"Monitoring {self.debug_dir} for new debug files (Ctrl+C to stop)...")
        
        try:
            while True:
                measurements = self.scan_files()
                self.process_new_measurements(measurements)
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\nStopping monitor...")

def main():
    parser = argparse.ArgumentParser(description='Debug Data Visualizer')
    parser.add_argument('--watch', action='store_true', 
                       help='Continuously monitor for new files')
    parser.add_argument('--debug-dir', default='dev_debug',
                       help='Debug directory to scan (default: dev_debug)')
    
    args = parser.parse_args()
    
    visualizer = DebugVisualizer(args.debug_dir)
    
    if args.watch:
        visualizer.run_watch()
    else:
        visualizer.run_once()

if __name__ == '__main__':
    main()
