# Makefile for ChunkProcessor Tests
# BladeRF Video Decoding Project - ChunkProcessor Module

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -pedantic
INCLUDES = -I. -I./tests

# Source directories
TEST_DIR = tests

# Source files for single test executable (header-only implementation)
TEST_SOURCES = $(TEST_DIR)/test_chunk_processor.cpp \
               $(TEST_DIR)/comprehensive_tests.cpp \
               $(TEST_DIR)/test_runner.cpp

# Object files
TEST_OBJECTS = $(TEST_SOURCES:.cpp=.o)

# Single test executable
TEST_EXECUTABLE = chunk_processor_tests

# Default target - build and run tests
all: test

# Build the single test executable
$(TEST_EXECUTABLE): $(TEST_OBJECTS)
	$(CXX) $(CXXFLAGS) -o $@ $^

# Object file compilation
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Build and run tests in one command
test: $(TEST_EXECUTABLE)
	@echo "Running all ChunkProcessor tests..."
	@echo "==================================="
	./$(TEST_EXECUTABLE)

# Build only (without running)
build: $(TEST_EXECUTABLE)
	@echo "ChunkProcessor test executable built: $(TEST_EXECUTABLE)"

# Quick verification (build and run with limited output)
verify: $(TEST_EXECUTABLE)
	@echo "Quick verification of ChunkProcessor..."
	@echo "======================================"
	@./$(TEST_EXECUTABLE) | grep -E "(PASS|FAIL|===|🎉|❌)" | head -15
	@echo "======================================"
	@echo "Run 'make test' for full output"

# Performance test (run only performance benchmarks)
perf: $(TEST_EXECUTABLE)
	@echo "Running ChunkProcessor performance tests..."
	@echo "==========================================="
	@./$(TEST_EXECUTABLE) | grep -A 20 "PERFORMANCE BENCHMARK"

# Clean build artifacts
clean:
	rm -f $(TEST_OBJECTS)
	rm -f $(TEST_EXECUTABLE)
	rm -f *.o tests/*.o

# Clean everything (same as clean for header-only implementation)
distclean: clean

# Install dependencies (none needed for header-only)
deps:
	@echo "No external dependencies required for ChunkProcessor tests"
	@echo "Header-only implementation - no separate compilation needed"

# Validate header-only implementation
validate-header:
	@echo "Validating header-only implementation..."
	@echo "======================================="
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only chunk_processor.h
	@echo "✓ Header syntax validation passed"
	@echo "✓ Header-only implementation confirmed"

# Code analysis
analyze: $(TEST_EXECUTABLE)
	@echo "Running static analysis on ChunkProcessor..."
	@echo "==========================================="
	@$(CXX) $(CXXFLAGS) $(INCLUDES) -Wall -Wextra -Wpedantic -fsyntax-only chunk_processor.h
	@echo "✓ Static analysis completed"

# Memory check (if valgrind is available)
memcheck: $(TEST_EXECUTABLE)
	@echo "Running memory check on ChunkProcessor..."
	@echo "========================================"
	@if command -v valgrind >/dev/null 2>&1; then \
		valgrind --leak-check=full --show-leak-kinds=all ./$(TEST_EXECUTABLE); \
	else \
		echo "Valgrind not available - skipping memory check"; \
		echo "Install valgrind for detailed memory analysis"; \
	fi

# Help target
help:
	@echo "ChunkProcessor Test Makefile"
	@echo "============================"
	@echo "Available targets:"
	@echo "  test           - Build and run all tests (default)"
	@echo "  build          - Build test executable only"
	@echo "  verify         - Quick verification with limited output"
	@echo "  perf           - Run performance benchmarks only"
	@echo "  validate-header- Validate header-only implementation"
	@echo "  analyze        - Run static code analysis"
	@echo "  memcheck       - Run memory leak detection (requires valgrind)"
	@echo "  clean          - Remove build artifacts"
	@echo "  deps           - Install dependencies (none needed)"
	@echo "  help           - Show this help message"
	@echo ""
	@echo "Simple usage:"
	@echo "  make test      # Build and run all tests"
	@echo "  make verify    # Quick verification"
	@echo "  make clean     # Clean up"
	@echo ""
	@echo "ChunkProcessor Features:"
	@echo "  - Template-based header-only implementation"
	@echo "  - Zero-copy optimization"
	@echo "  - 64-bit overflow protection"
	@echo "  - Specification compliant"
	@echo "  - Comprehensive test coverage"

# Phony targets
.PHONY: all test build verify perf clean distclean deps validate-header analyze memcheck help

# Dependency tracking for header-only implementation
$(TEST_DIR)/test_chunk_processor.o: $(TEST_DIR)/test_chunk_processor.cpp chunk_processor.h

$(TEST_DIR)/comprehensive_tests.o: $(TEST_DIR)/comprehensive_tests.cpp chunk_processor.h

$(TEST_DIR)/test_runner.o: $(TEST_DIR)/test_runner.cpp

# Additional compiler flags for different build types
debug: CXXFLAGS += -DDEBUG -O0 -g3
debug: $(TEST_EXECUTABLE)

release: CXXFLAGS += -DNDEBUG -O3 -flto
release: $(TEST_EXECUTABLE)

# Profile-guided optimization (if supported)
pgo-generate: CXXFLAGS += -fprofile-generate
pgo-generate: $(TEST_EXECUTABLE)
	@echo "Running PGO data collection..."
	./$(TEST_EXECUTABLE) > /dev/null

pgo-use: CXXFLAGS += -fprofile-use
pgo-use: $(TEST_EXECUTABLE)
