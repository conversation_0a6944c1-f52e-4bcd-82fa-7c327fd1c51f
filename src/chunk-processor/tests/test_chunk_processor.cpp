#include "../chunk_processor.h"
#include <iostream>
#include <vector>
#include <algorithm>
#include <numeric>
#include <cassert>
#include <random>

// Test utilities
class TestUtils {
public:
    static void assert_true(bool condition, const std::string& message) {
        if (!condition) {
            std::cerr << "❌ ASSERTION FAILED: " << message << std::endl;
            exit(1);
        }
        std::cout << "✓ PASS: " << message << std::endl;
    }
    
    static void assert_equal(size_t expected, size_t actual, const std::string& message) {
        if (expected != actual) {
            std::cerr << "❌ ASSERTION FAILED: " << message 
                      << " (expected: " << expected << ", actual: " << actual << ")" << std::endl;
            exit(1);
        }
        std::cout << "✓ PASS: " << message << std::endl;
    }

    static void print_test_header(const std::string& test_name) {
        std::cout << "\n" << std::string(50, '-') << std::endl;
        std::cout << "TEST: " << test_name << std::endl;
        std::cout << std::string(50, '-') << std::endl;
    }
};

// Chunk data collector for testing (template-based)
template<typename SampleType = uint32_t>
struct ChunkData {
    std::vector<SampleType> data;
    size_t chunk_index;

    ChunkData(const SampleType* ptr, size_t len, size_t idx)
        : data(ptr, ptr + len), chunk_index(idx) {}
};

template<typename SampleType = uint32_t>
class ChunkCollector {
public:
    std::vector<ChunkData<SampleType>> chunks;

    void operator()(const SampleType* data, size_t length) {
        chunks.emplace_back(data, length, chunks.size());
    }
    
    void clear() {
        chunks.clear();
    }
    
    size_t count() const {
        return chunks.size();
    }
};

// Test 1: Basic construction and parameter validation
void test_construction_and_validation() {
    TestUtils::print_test_header("Construction and Parameter Validation");

    // Test valid construction with default template parameter
    ChunkCollector<> collector;
    auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector(data, chunkSize);
    };

    ChunkProcessor<> processor(1024, 512, 128, 4, handler);
    TestUtils::assert_true(true, "Valid construction succeeds with default template parameter");

    // Test construction with explicit template parameter
    ChunkCollector<uint32_t> collector2;
    auto handler2 = [&collector2](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector2(data, chunkSize);
    };

    ChunkProcessor<uint32_t> processor2(1024, 512, 128, 4, handler2);
    TestUtils::assert_true(true, "Valid construction succeeds with explicit template parameter");

    std::cout << "Construction validation tests completed." << std::endl;
}

// Test 2: Basic write and read operations
void test_basic_write_read() {
    TestUtils::print_test_header("Basic Write and Read Operations");

    ChunkCollector<> collector;
    auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector(data, chunkSize);
    };

    // Simple configuration: WC=1024, RC=200, OV=50
    ChunkProcessor<> processor(1024, 200, 50, 4, handler);

    // Fill first write chunk with test data
    uint32_t* writePtr = processor.getWriteChunkPtr();
    for (size_t i = 0; i < 1024; ++i) {
        writePtr[i] = static_cast<uint32_t>(i);
    }

    // Commit and check for read chunks
    processor.commitWriteChunk();

    // Should generate multiple read chunks as per specification example
    TestUtils::assert_true(collector.count() > 0, "Read chunks generated after first commit");
    TestUtils::assert_equal(200, collector.chunks[0].data.size(), "First read chunk has correct size");

    // Verify data integrity in first chunk
    bool data_correct = true;
    for (size_t i = 0; i < 200; ++i) {
        if (collector.chunks[0].data[i] != i) {
            data_correct = false;
            break;
        }
    }
    TestUtils::assert_true(data_correct, "First read chunk contains correct data");

    std::cout << "Basic write/read operations completed." << std::endl;
}

// Test 3: Overlap verification
void test_overlap_verification() {
    TestUtils::print_test_header("Overlap Verification");

    ChunkCollector<> collector;
    auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector(data, chunkSize);
    };

    // Configuration: WC=1024, RC=200, OV=50 (stride=150)
    ChunkProcessor<> processor(1024, 200, 50, 4, handler);

    // Fill write chunk with sequential data
    uint32_t* writePtr = processor.getWriteChunkPtr();
    for (size_t i = 0; i < 1024; ++i) {
        writePtr[i] = static_cast<uint32_t>(i);
    }

    processor.commitWriteChunk();

    // Verify overlap between consecutive chunks
    if (collector.count() >= 2) {
        const auto& chunk1 = collector.chunks[0];
        const auto& chunk2 = collector.chunks[1];

        // Check overlap: last 50 elements of chunk1 should match first 50 elements of chunk2
        bool overlap_correct = true;
        for (size_t i = 0; i < 50; ++i) {
            if (chunk1.data[150 + i] != chunk2.data[i]) {
                overlap_correct = false;
                break;
            }
        }
        TestUtils::assert_true(overlap_correct, "Overlap between consecutive chunks is correct");
    }

    std::cout << "Overlap verification completed." << std::endl;
}

// Test 4: Multiple write chunks
void test_multiple_writes() {
    TestUtils::print_test_header("Multiple Write Chunks");

    ChunkCollector<> collector;
    auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector(data, chunkSize);
    };

    ChunkProcessor<> processor(512, 300, 100, 4, handler);
    
    size_t initial_count = collector.count();
    
    // Perform multiple writes
    for (int write_num = 0; write_num < 3; ++write_num) {
        uint32_t* writePtr = processor.getWriteChunkPtr();
        for (size_t i = 0; i < 512; ++i) {
            writePtr[i] = static_cast<uint32_t>(write_num * 1000 + i);
        }
        processor.commitWriteChunk();
    }
    
    TestUtils::assert_true(collector.count() > initial_count, "Multiple writes generate read chunks");
    
    std::cout << "Multiple write chunks test completed." << std::endl;
}

// Test 5: Buffer wrap-around (glue buffer usage)
void test_buffer_wraparound() {
    TestUtils::print_test_header("Buffer Wrap-around (Glue Buffer)");

    ChunkCollector<> collector;
    auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector(data, chunkSize);
    };

    // Small buffer to force wrap-around quickly
    ChunkProcessor<> processor(100, 150, 25, 3, handler);
    
    // Fill buffer to force wrap-around
    for (int i = 0; i < 5; ++i) {
        uint32_t* writePtr = processor.getWriteChunkPtr();
        for (size_t j = 0; j < 100; ++j) {
            writePtr[j] = static_cast<uint32_t>(i * 100 + j);
        }
        processor.commitWriteChunk();
    }
    
    TestUtils::assert_true(collector.count() > 0, "Wrap-around handling produces read chunks");
    
    // Verify data integrity across wrap-around
    if (collector.count() > 0) {
        bool data_consistent = true;
        for (const auto& chunk : collector.chunks) {
            if (chunk.data.size() != 150) {
                data_consistent = false;
                break;
            }
        }
        TestUtils::assert_true(data_consistent, "All chunks have correct size after wrap-around");
    }
    
    std::cout << "Buffer wrap-around test completed." << std::endl;
}

// Test 6: Randomized data validation with deterministic seed
void test_randomized_data_validation() {
    TestUtils::print_test_header("Randomized Data Validation");

    ChunkCollector<> collector;
    auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector(data, chunkSize);
    };

    ChunkProcessor<> processor(512, 256, 64, 6, handler);

    // Use deterministic random number generation for reproducible results
    std::mt19937 rng(42);  // Fixed seed for reproducibility
    std::uniform_int_distribution<uint32_t> dist(0, 0xFFFFFFFF);

    // Generate and store expected data
    std::vector<uint32_t> expected_data;

    // Perform multiple writes with random data
    for (int write_num = 0; write_num < 5; ++write_num) {
        uint32_t* writePtr = processor.getWriteChunkPtr();
        for (size_t i = 0; i < 512; ++i) {
            uint32_t value = dist(rng);
            writePtr[i] = value;
            expected_data.push_back(value);
        }
        processor.commitWriteChunk();
    }

    // Validate that all read chunks contain correct data
    bool data_valid = true;
    size_t stride = 256 - 64;  // 192

    for (size_t chunk_idx = 0; chunk_idx < collector.chunks.size(); ++chunk_idx) {
        const auto& chunk = collector.chunks[chunk_idx];
        size_t expected_start = chunk_idx * stride;

        for (size_t i = 0; i < chunk.data.size() && expected_start + i < expected_data.size(); ++i) {
            if (chunk.data[i] != expected_data[expected_start + i]) {
                data_valid = false;
                break;
            }
        }
        if (!data_valid) break;
    }

    TestUtils::assert_true(data_valid, "Randomized data validation passed");
    TestUtils::assert_true(collector.count() > 0, "Random data generated read chunks");

    std::cout << "Randomized data validation completed." << std::endl;
}

// Test 7: Edge case tests
void test_edge_cases() {
    TestUtils::print_test_header("Edge Case Tests");

    // Test 7a: Single-sample writes and reads
    {
        ChunkCollector<> collector;
        auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
            collector(data, chunkSize);
        };

        ChunkProcessor<> processor(1, 1, 0, 10, handler);

        for (int i = 0; i < 5; ++i) {
            uint32_t* writePtr = processor.getWriteChunkPtr();
            writePtr[0] = static_cast<uint32_t>(i * 100);
            processor.commitWriteChunk();
        }

        TestUtils::assert_true(collector.count() >= 5, "Single-sample operations work");
    }

    // Test 7b: Maximum possible overlap
    {
        ChunkCollector<> collector;
        auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
            collector(data, chunkSize);
        };

        ChunkProcessor<> processor(1000, 500, 499, 4, handler);  // 99.8% overlap

        uint32_t* writePtr = processor.getWriteChunkPtr();
        for (size_t i = 0; i < 1000; ++i) {
            writePtr[i] = static_cast<uint32_t>(i);
        }
        processor.commitWriteChunk();

        TestUtils::assert_true(collector.count() > 0, "Maximum overlap scenario works");

        // Verify extreme overlap
        if (collector.count() >= 2) {
            const auto& chunk1 = collector.chunks[0];
            const auto& chunk2 = collector.chunks[1];

            bool overlap_correct = true;
            for (size_t i = 0; i < 499; ++i) {
                if (chunk1.data[1 + i] != chunk2.data[i]) {
                    overlap_correct = false;
                    break;
                }
            }
            TestUtils::assert_true(overlap_correct, "Extreme overlap data integrity maintained");
        }
    }

    // Test 7c: Buffer boundary conditions
    {
        ChunkCollector<> collector;
        auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
            collector(data, chunkSize);
        };

        // Configure to hit exact buffer boundaries
        ChunkProcessor<> processor(256, 256, 0, 4, handler);  // Exact fit

        uint32_t* writePtr = processor.getWriteChunkPtr();
        for (size_t i = 0; i < 256; ++i) {
            writePtr[i] = static_cast<uint32_t>(i);
        }
        processor.commitWriteChunk();

        TestUtils::assert_true(collector.count() == 1, "Buffer boundary exact fit works");
        TestUtils::assert_equal(256, collector.chunks[0].data.size(), "Boundary chunk has correct size");
    }

    std::cout << "Edge case tests completed." << std::endl;
}

// Test 8: Explicit glue buffer validation
void test_glue_buffer_validation() {
    TestUtils::print_test_header("Glue Buffer Validation");

    ChunkCollector<> collector;
    auto handler = [&collector](const uint32_t* data, size_t chunkSize, size_t overlapSize) {
        collector(data, chunkSize);
    };

    // Small buffer configuration to force wrap-around quickly
    ChunkProcessor<> processor(100, 150, 25, 3, handler);  // Buffer = 300 samples

    // Fill buffer with known pattern to force wrap-around
    uint32_t pattern_value = 0x12345678;
    for (int write_idx = 0; write_idx < 6; ++write_idx) {
        uint32_t* writePtr = processor.getWriteChunkPtr();
        for (size_t i = 0; i < 100; ++i) {
            writePtr[i] = pattern_value + (write_idx * 100) + i;
        }
        processor.commitWriteChunk();
    }

    // Verify data continuity across wrap-around boundaries
    bool wrap_around_valid = true;
    size_t stride = 150 - 25;  // 125

    for (size_t chunk_idx = 1; chunk_idx < collector.chunks.size(); ++chunk_idx) {
        const auto& prev_chunk = collector.chunks[chunk_idx - 1];
        const auto& curr_chunk = collector.chunks[chunk_idx];

        // Verify overlap region
        for (size_t i = 0; i < 25; ++i) {
            if (prev_chunk.data[stride + i] != curr_chunk.data[i]) {
                wrap_around_valid = false;
                break;
            }
        }
        if (!wrap_around_valid) break;

        // Verify data progression
        uint32_t expected_start = prev_chunk.data[0] + stride;
        if (curr_chunk.data[0] != expected_start) {
            wrap_around_valid = false;
            break;
        }
    }

    TestUtils::assert_true(wrap_around_valid, "Glue buffer maintains data integrity across wrap-around");
    TestUtils::assert_true(collector.count() > 3, "Wrap-around scenario generated multiple chunks");

    std::cout << "Glue buffer validation completed." << std::endl;
}

// Main test runner function
int run_chunk_processor_tests() {
    std::cout << "\n🧪 Running ChunkProcessor Core Functionality Tests" << std::endl;
    std::cout << "==================================================" << std::endl;

    try {
        test_construction_and_validation();
        test_basic_write_read();
        test_overlap_verification();
        test_multiple_writes();
        test_buffer_wraparound();
        test_randomized_data_validation();
        test_edge_cases();
        test_glue_buffer_validation();

        std::cout << "\n🎉 All enhanced core functionality tests PASSED!" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "\n❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "\n❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
}
